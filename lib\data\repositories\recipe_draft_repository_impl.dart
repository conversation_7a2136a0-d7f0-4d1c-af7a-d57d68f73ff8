import 'dart:async';
import 'package:sqflite/sqflite.dart';

import '../../core/errors/exceptions.dart' as app_exceptions;
import '../../domain/entities/recipe_draft.dart';
import '../../domain/repositories/recipe_draft_repository.dart';
import '../datasources/database_helper.dart';
import '../models/recipe_draft_model.dart';

/// RecipeDraft repository implementation with SQLite backend
///
/// [Source: Story 1.3 - Task 3]
class RecipeDraftRepositoryImpl implements RecipeDraftRepository {
  final DatabaseHelper _databaseHelper;

  // Performance requirement: CRUD operations under 500ms
  static const Duration _operationTimeout = Duration(milliseconds: 500);

  RecipeDraftRepositoryImpl(this._databaseHelper);

  @override
  Future<void> createDraft(RecipeDraft draft) async {
    try {
      // Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final draftModel = RecipeDraftModel.fromEntity(draft);

      await _executeWithTimeout(() => _databaseHelper.transaction((txn) async {
        await txn.insert(
          'recipe_drafts',
          draftModel.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }));
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to create recipe draft: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<RecipeDraft?> getDraftById(String id) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'recipe_drafts',
          where: 'id = ?',
          whereArgs: [id],
          limit: 1,
        );
      });

      if (maps.isEmpty) return null;
      return RecipeDraftModel.fromMap(maps.first).toEntity();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get recipe draft by ID: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<List<RecipeDraft>> getAllDrafts() async {
    try {
      final db = await _databaseHelper.database;

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'recipe_drafts',
          orderBy: 'created_at DESC',
        );
      });

      return maps.map((map) => RecipeDraftModel.fromMap(map).toEntity()).toList();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get all recipe drafts: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> updateDraft(RecipeDraft draft) async {
    try {
      // Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final draftModel = RecipeDraftModel.fromEntity(draft.copyWith(
        updatedAt: DateTime.now(),
      ));

      final rowsAffected = await _executeWithTimeout(() => _databaseHelper.transaction((txn) async {
        return await txn.update(
          'recipe_drafts',
          draftModel.toMap(),
          where: 'id = ?',
          whereArgs: [draft.id],
        );
      }));

      if (rowsAffected == 0) {
        throw app_exceptions.EntityNotFoundException('RecipeDraft', draft.id);
      }
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.EntityNotFoundException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to update recipe draft: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> deleteDraft(String id) async {
    try {
      // Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final rowsAffected = await _executeWithTimeout(() => _databaseHelper.transaction((txn) async {
        return await txn.delete(
          'recipe_drafts',
          where: 'id = ?',
          whereArgs: [id],
        );
      }));

      if (rowsAffected == 0) {
        throw app_exceptions.EntityNotFoundException('RecipeDraft', id);
      }
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.EntityNotFoundException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to delete recipe draft: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> deleteAllDrafts() async {
    try {
      // Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      await _executeWithTimeout(() => _databaseHelper.transaction((txn) async {
        await txn.delete('recipe_drafts');
      }));
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to delete all recipe drafts: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<int> getDraftsCount() async {
    try {
      final db = await _databaseHelper.database;

      final result = await _executeWithTimeout(() async {
        return await db.rawQuery('SELECT COUNT(*) as count FROM recipe_drafts');
      });

      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get recipe drafts count: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<bool> draftExists(String id) async {
    try {
      final draft = await getDraftById(id);
      return draft != null;
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.DatabaseException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to check if recipe draft exists: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Execute operation with timeout enforcement (500ms requirement)
  Future<T> _executeWithTimeout<T>(Future<T> Function() operation) async {
    try {
      return await operation().timeout(_operationTimeout);
    } on TimeoutException {
      throw app_exceptions.PerformanceException(
        'Recipe draft repository operation exceeded timeout limit',
        timeout: _operationTimeout,
      );
    }
  }
}
