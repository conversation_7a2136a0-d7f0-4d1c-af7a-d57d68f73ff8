import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart' as path;

import '../../lib/data/datasources/database_helper.dart';
import '../../lib/data/repositories/recipe_repository_impl.dart';
import '../../lib/domain/entities/recipe.dart';
import '../../lib/domain/entities/ingredient.dart';
import '../../lib/domain/entities/procedure_step.dart';

void main() {
  group('Database Backup Integration Tests', () {
    late DatabaseHelper databaseHelper;
    late RecipeRepositoryImpl recipeRepository;

    setUpAll(() {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() async {
      databaseHelper = DatabaseHelper();
      recipeRepository = RecipeRepositoryImpl(databaseHelper);
    });

    tearDown(() async {
      await databaseHelper.close();
    });

    testWidgets('Recipe operations trigger backup before execution',
        (WidgetTester tester) async {
      // Arrange
      final testRecipe = Recipe(
        id: 'test-recipe-1',
        name: 'Test Recipe',
        description: 'Test Description',
        ingredients: [
          const Ingredient(name: 'Test Ingredient', measurement: '100ml'),
        ],
        procedures: [
          const ProcedureStep(stepNumber: 1, instruction: 'Test step'),
        ],
        difficultyLevel: 'beginner',
        estimatedTime: 60,
        isPremium: false,
        successRate: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Act - Create recipe (should trigger backup)
      await recipeRepository.createRecipe(testRecipe);

      // Assert - Recipe should be created successfully
      final createdRecipe =
          await recipeRepository.getRecipeById('test-recipe-1');
      expect(createdRecipe, isNotNull);
      expect(createdRecipe!.name, equals(testRecipe.name));
    });

    testWidgets('Backup operations complete within performance requirements',
        (WidgetTester tester) async {
      // Arrange
      final testRecipe = Recipe(
        id: 'perf-test-recipe',
        name: 'Performance Test Recipe',
        description: 'Testing backup performance',
        ingredients: [
          const Ingredient(name: 'Test Ingredient', measurement: '100ml'),
        ],
        procedures: [
          const ProcedureStep(stepNumber: 1, instruction: 'Test step'),
        ],
        difficultyLevel: 'advanced',
        estimatedTime: 120,
        isPremium: false,
        successRate: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Act & Assert - Operation should complete within timeout
      final stopwatch = Stopwatch()..start();

      await recipeRepository.createRecipe(testRecipe);

      stopwatch.stop();

      // Backup should not significantly impact operation performance
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
    });
  });
}
