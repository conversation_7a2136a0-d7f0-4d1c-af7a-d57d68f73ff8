import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../shared/services/recipe_count_service.dart';
import '../providers/core_providers.dart';

/// Usage indicator widget showing recipe count and limits
///
/// [Source: Story 1.3 - Task 2: AC 2, 3]
class UsageIndicatorWidget extends ConsumerWidget {
  final bool showWarning;
  final VoidCallback? onUpgradePressed;

  const UsageIndicatorWidget({
    super.key,
    this.showWarning = false,
    this.onUpgradePressed,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final deviceIdAsync = ref.watch(deviceIdProvider);
    final recipeCountService = ref.watch(recipeCountServiceProvider);

    return deviceIdAsync.when(
      data: (deviceId) => _buildUsageIndicator(context, recipeCountService, deviceId),
      loading: () => const _LoadingIndicator(),
      error: (error, stack) => _ErrorIndicator(error: error),
    );
  }

  Widget _buildUsageIndicator(BuildContext context, RecipeCountService service, String deviceId) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getUsageData(service, deviceId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const _LoadingIndicator();
        }

        if (snapshot.hasError) {
          return _ErrorIndicator(error: snapshot.error);
        }

        final data = snapshot.data!;
        final usageText = data['usageText'] as String;
        final shouldShowWarning = data['shouldShowWarning'] as bool;
        final hasReachedLimit = data['hasReachedLimit'] as bool;
        final isPremium = data['isPremium'] as bool;

        return _buildIndicatorCard(
          context,
          usageText: usageText,
          shouldShowWarning: shouldShowWarning,
          hasReachedLimit: hasReachedLimit,
          isPremium: isPremium,
        );
      },
    );
  }

  Future<Map<String, dynamic>> _getUsageData(RecipeCountService service, String deviceId) async {
    final usageText = await service.getUsageIndicatorText(deviceId);
    final shouldShowWarning = await service.shouldShowWarning(deviceId);
    final hasReachedLimit = await service.hasReachedLimit(deviceId);
    final isPremium = await service.getRemainingSlots(deviceId) == -1; // -1 indicates unlimited

    return {
      'usageText': usageText,
      'shouldShowWarning': shouldShowWarning,
      'hasReachedLimit': hasReachedLimit,
      'isPremium': isPremium,
    };
  }

  Widget _buildIndicatorCard(
    BuildContext context, {
    required String usageText,
    required bool shouldShowWarning,
    required bool hasReachedLimit,
    required bool isPremium,
  }) {
    Color cardColor = Colors.grey.shade100;
    Color textColor = Colors.grey.shade700;
    IconData icon = Icons.info_outline;

    if (isPremium) {
      cardColor = Colors.green.shade50;
      textColor = Colors.green.shade700;
      icon = Icons.star;
    } else if (hasReachedLimit) {
      cardColor = Colors.red.shade50;
      textColor = Colors.red.shade700;
      icon = Icons.warning;
    } else if (shouldShowWarning) {
      cardColor = Colors.orange.shade50;
      textColor = Colors.orange.shade700;
      icon = Icons.warning_amber;
    }

    return Card(
      color: cardColor,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Icon(icon, color: textColor, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                usageText,
                style: TextStyle(
                  color: textColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (hasReachedLimit && !isPremium && onUpgradePressed != null) ...[
              const SizedBox(width: 8),
              TextButton(
                onPressed: onUpgradePressed,
                child: const Text('Upgrade'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _LoadingIndicator extends StatelessWidget {
  const _LoadingIndicator();

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.grey.shade100,
      child: const Padding(
        padding: EdgeInsets.all(12.0),
        child: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            SizedBox(width: 8),
            Text('Loading usage...'),
          ],
        ),
      ),
    );
  }
}

class _ErrorIndicator extends StatelessWidget {
  final Object? error;

  const _ErrorIndicator({required this.error});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red.shade700, size: 20),
            const SizedBox(width: 8),
            const Expanded(
              child: Text(
                'Unable to load usage information',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
