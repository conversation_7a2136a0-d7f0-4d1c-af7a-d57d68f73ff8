import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culturestack/data/datasources/database_helper.dart';
import 'package:culturestack/core/utils/logger.dart';
import 'package:culturestack/presentation/screens/recipe_management_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    final dbHelper = DatabaseHelper();
    final isHealthy = await dbHelper.checkDatabaseHealth();

    if (!isHealthy) {
      try {
        await dbHelper.restoreDatabase();
      } catch (e) {
        // Handle restore error, maybe show a dialog to the user
        try {
          await Logger().log('Database restore failed: $e');
        } catch (_) {
          // Ignore logging errors during startup
        }
      }
    }
  } catch (e) {
    // Handle database initialization errors gracefully
    try {
      await Logger().log('Database initialization failed: $e');
    } catch (_) {
      // Ignore logging errors during startup
    }
    // App can still start with limited functionality
  }

  runApp(const ProviderScope(child: CultureStackApp()));
}

class CultureStackApp extends StatelessWidget {
  const CultureStackApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Culture Stack',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.green),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          centerTitle: true,
          elevation: 2,
        ),
        cardTheme: CardThemeData(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
      home: const RecipeManagementScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
