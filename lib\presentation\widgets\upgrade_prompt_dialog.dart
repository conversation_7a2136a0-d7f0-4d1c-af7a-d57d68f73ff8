import 'package:flutter/material.dart';

/// Upgrade prompt dialog shown when users hit recipe limits
///
/// [Source: Story 1.3 - Task 2: AC 4, 7]
class UpgradePromptDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onUpgradePressed;
  final VoidCallback? onSaveDraftPressed;
  final bool showSaveDraftOption;

  const UpgradePromptDialog({
    super.key,
    required this.title,
    required this.message,
    this.onUpgradePressed,
    this.onSaveDraftPressed,
    this.showSaveDraftOption = true,
  });

  /// Factory constructor for recipe limit reached scenario
  factory UpgradePromptDialog.recipeLimitReached({
    VoidCallback? onUpgradePressed,
    VoidCallback? onSaveDraftPressed,
  }) {
    return UpgradePromptDialog(
      title: 'Recipe Limit Reached',
      message: 'You\'ve reached the free limit of 10 recipes. Upgrade to Premium to create unlimited recipes and unlock advanced features.',
      onUpgradePressed: onUpgradePressed,
      onSaveDraftPressed: onSaveDraftPressed,
      showSaveDraftOption: true,
    );
  }

  /// Factory constructor for warning at 8/10 recipes
  factory UpgradePromptDialog.warningPrompt({
    VoidCallback? onUpgradePressed,
  }) {
    return UpgradePromptDialog(
      title: 'Almost at Recipe Limit',
      message: 'You\'re using 8 out of 10 free recipes. Consider upgrading to Premium for unlimited recipes.',
      onUpgradePressed: onUpgradePressed,
      showSaveDraftOption: false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.star,
            color: Colors.amber.shade600,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(title),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(message),
          const SizedBox(height: 16),
          _buildPremiumFeatures(),
        ],
      ),
      actions: [
        if (showSaveDraftOption && onSaveDraftPressed != null)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onSaveDraftPressed!();
            },
            child: const Text('Save as Draft'),
          ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Not Now'),
        ),
        ElevatedButton(
          onPressed: onUpgradePressed != null
              ? () {
                  Navigator.of(context).pop();
                  onUpgradePressed!();
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.amber.shade600,
            foregroundColor: Colors.white,
          ),
          child: const Text('Upgrade to Premium'),
        ),
      ],
    );
  }

  Widget _buildPremiumFeatures() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Premium Features:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.amber.shade800,
            ),
          ),
          const SizedBox(height: 8),
          _buildFeatureItem('Unlimited recipes'),
          _buildFeatureItem('Advanced search and filters'),
          _buildFeatureItem('Recipe export and sharing'),
          _buildFeatureItem('Priority support'),
          _buildFeatureItem('Ad-free experience'),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String feature) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green.shade600,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            feature,
            style: TextStyle(
              color: Colors.grey.shade700,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Show the upgrade prompt dialog
  static Future<void> show(
    BuildContext context, {
    required String title,
    required String message,
    VoidCallback? onUpgradePressed,
    VoidCallback? onSaveDraftPressed,
    bool showSaveDraftOption = true,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => UpgradePromptDialog(
        title: title,
        message: message,
        onUpgradePressed: onUpgradePressed,
        onSaveDraftPressed: onSaveDraftPressed,
        showSaveDraftOption: showSaveDraftOption,
      ),
    );
  }

  /// Show recipe limit reached dialog
  static Future<void> showRecipeLimitReached(
    BuildContext context, {
    VoidCallback? onUpgradePressed,
    VoidCallback? onSaveDraftPressed,
  }) {
    return show(
      context,
      title: 'Recipe Limit Reached',
      message: 'You\'ve reached the free limit of 10 recipes. Upgrade to Premium to create unlimited recipes and unlock advanced features.',
      onUpgradePressed: onUpgradePressed,
      onSaveDraftPressed: onSaveDraftPressed,
      showSaveDraftOption: true,
    );
  }

  /// Show warning prompt at 8/10 recipes
  static Future<void> showWarningPrompt(
    BuildContext context, {
    VoidCallback? onUpgradePressed,
  }) {
    return show(
      context,
      title: 'Almost at Recipe Limit',
      message: 'You\'re using 8 out of 10 free recipes. Consider upgrading to Premium for unlimited recipes.',
      onUpgradePressed: onUpgradePressed,
      showSaveDraftOption: false,
    );
  }
}
