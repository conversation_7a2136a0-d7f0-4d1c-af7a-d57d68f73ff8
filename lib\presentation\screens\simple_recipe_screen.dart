import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'recipe_management_screen.dart';

/// Simple recipe screen for testing
class SimpleRecipeScreen extends ConsumerWidget {
  const SimpleRecipeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CultureStack - Recipe Management'),
        centerTitle: true,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.restaurant_menu,
              size: 64,
              color: Colors.green,
            ),
            SizedBox(height: 16),
            Text(
              'Welcome to CultureStack!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Your recipe management app is ready.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 32),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Features Available:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 12),
                    ListTile(
                      leading: Icon(Icons.add_circle, color: Colors.green),
                      title: Text('Create Recipes'),
                      subtitle: Text('Add ingredients and procedures'),
                    ),
                    ListTile(
                      leading: Icon(Icons.search, color: Colors.blue),
                      title: Text('Search & Filter'),
                      subtitle: Text('Find recipes by ingredients'),
                    ),
                    ListTile(
                      leading: Icon(Icons.drafts, color: Colors.orange),
                      title: Text('Draft System'),
                      subtitle: Text('Auto-save your work'),
                    ),
                    ListTile(
                      leading: Icon(Icons.star, color: Colors.amber),
                      title: Text('Premium Features'),
                      subtitle: Text('Unlimited recipes & more'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton.extended(
            heroTag: "full_app",
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const RecipeManagementScreen(),
                ),
              );
            },
            icon: const Icon(Icons.restaurant_menu),
            label: const Text('Full App'),
            backgroundColor: Colors.green,
          ),
          const SizedBox(height: 8),
          FloatingActionButton.extended(
            heroTag: "create_recipe",
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content:
                      Text('Tap "Full App" to see complete recipe management!'),
                ),
              );
            },
            icon: const Icon(Icons.add),
            label: const Text('Create Recipe'),
          ),
        ],
      ),
    );
  }
}
