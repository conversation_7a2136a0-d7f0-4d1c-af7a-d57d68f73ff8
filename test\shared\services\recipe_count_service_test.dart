import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:culturestack/shared/services/recipe_count_service.dart';
import 'package:culturestack/domain/repositories/recipe_repository.dart';
import 'package:culturestack/domain/repositories/premium_repository.dart';

import 'recipe_count_service_test.mocks.dart';

@GenerateMocks([RecipeRepository, PremiumRepository])
void main() {
  group('RecipeCountService', () {
    late RecipeCountService service;
    late MockRecipeRepository mockRecipeRepository;
    late MockPremiumRepository mockPremiumRepository;

    const testDeviceId = 'test-device-id';

    setUp(() {
      mockRecipeRepository = MockRecipeRepository();
      mockPremiumRepository = MockPremiumRepository();
      service = RecipeCountService(mockRecipeRepository, mockPremiumRepository);
    });

    group('getCurrentRecipeCount', () {
      test('should return recipe count from repository', () async {
        // Arrange
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 5);

        // Act
        final result = await service.getCurrentRecipeCount();

        // Assert
        expect(result, equals(5));
        verify(mockRecipeRepository.getRecipesCount()).called(1);
      });
    });

    group('canCreateRecipe', () {
      test('should return true for premium users regardless of count', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => true);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 15);

        // Act
        final result = await service.canCreateRecipe(testDeviceId);

        // Assert
        expect(result, isTrue);
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verifyNever(mockRecipeRepository.getRecipesCount());
      });

      test('should return true for free users under limit', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => false);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 5);

        // Act
        final result = await service.canCreateRecipe(testDeviceId);

        // Assert
        expect(result, isTrue);
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verify(mockRecipeRepository.getRecipesCount()).called(1);
      });

      test('should return false for free users at limit', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => false);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 10);

        // Act
        final result = await service.canCreateRecipe(testDeviceId);

        // Assert
        expect(result, isFalse);
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verify(mockRecipeRepository.getRecipesCount()).called(1);
      });
    });

    group('shouldShowWarning', () {
      test('should return false for premium users', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => true);

        // Act
        final result = await service.shouldShowWarning(testDeviceId);

        // Assert
        expect(result, isFalse);
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verifyNever(mockRecipeRepository.getRecipesCount());
      });

      test('should return true for free users at warning threshold', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => false);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 8);

        // Act
        final result = await service.shouldShowWarning(testDeviceId);

        // Assert
        expect(result, isTrue);
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verify(mockRecipeRepository.getRecipesCount()).called(1);
      });

      test('should return false for free users below warning threshold', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => false);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 5);

        // Act
        final result = await service.shouldShowWarning(testDeviceId);

        // Assert
        expect(result, isFalse);
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verify(mockRecipeRepository.getRecipesCount()).called(1);
      });
    });

    group('hasReachedLimit', () {
      test('should return false for premium users', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => true);

        // Act
        final result = await service.hasReachedLimit(testDeviceId);

        // Assert
        expect(result, isFalse);
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verifyNever(mockRecipeRepository.getRecipesCount());
      });

      test('should return true for free users at limit', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => false);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 10);

        // Act
        final result = await service.hasReachedLimit(testDeviceId);

        // Assert
        expect(result, isTrue);
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verify(mockRecipeRepository.getRecipesCount()).called(1);
      });
    });

    group('getUsageIndicatorText', () {
      test('should return premium text for premium users', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => true);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 15);

        // Act
        final result = await service.getUsageIndicatorText(testDeviceId);

        // Assert
        expect(result, equals('15 recipes (Premium)'));
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verify(mockRecipeRepository.getRecipesCount()).called(1);
      });

      test('should return count text for free users', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => false);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 7);

        // Act
        final result = await service.getUsageIndicatorText(testDeviceId);

        // Assert
        expect(result, equals('7/10 recipes used'));
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verify(mockRecipeRepository.getRecipesCount()).called(1);
      });
    });

    group('getRemainingSlots', () {
      test('should return -1 for premium users (unlimited)', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => true);

        // Act
        final result = await service.getRemainingSlots(testDeviceId);

        // Assert
        expect(result, equals(-1));
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verifyNever(mockRecipeRepository.getRecipesCount());
      });

      test('should return remaining slots for free users', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => false);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 3);

        // Act
        final result = await service.getRemainingSlots(testDeviceId);

        // Assert
        expect(result, equals(7));
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verify(mockRecipeRepository.getRecipesCount()).called(1);
      });

      test('should return 0 when at limit', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => false);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 10);

        // Act
        final result = await service.getRemainingSlots(testDeviceId);

        // Assert
        expect(result, equals(0));
        verify(mockPremiumRepository.isPremiumUser(testDeviceId)).called(1);
        verify(mockRecipeRepository.getRecipesCount()).called(1);
      });
    });

    group('needsUpgradePrompt', () {
      test('should return true when user has reached limit', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => false);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 10);

        // Act
        final result = await service.needsUpgradePrompt(testDeviceId);

        // Assert
        expect(result, isTrue);
      });

      test('should return false when user has not reached limit', () async {
        // Arrange
        when(mockPremiumRepository.isPremiumUser(testDeviceId))
            .thenAnswer((_) async => false);
        when(mockRecipeRepository.getRecipesCount()).thenAnswer((_) async => 5);

        // Act
        final result = await service.needsUpgradePrompt(testDeviceId);

        // Assert
        expect(result, isFalse);
      });
    });
  });
}
