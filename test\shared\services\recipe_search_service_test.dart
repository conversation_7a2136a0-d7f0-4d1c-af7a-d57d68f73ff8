import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:culturestack/shared/services/recipe_search_service.dart';
import 'package:culturestack/domain/repositories/recipe_repository.dart';
import 'package:culturestack/domain/entities/recipe.dart';
import 'package:culturestack/domain/entities/ingredient.dart';
import 'package:culturestack/domain/entities/procedure_step.dart';

import 'recipe_search_service_test.mocks.dart';

@GenerateMocks([RecipeRepository])
void main() {
  group('RecipeSearchService', () {
    late RecipeSearchService service;
    late MockRecipeRepository mockRecipeRepository;

    setUp(() {
      mockRecipeRepository = MockRecipeRepository();
      service = RecipeSearchService(mockRecipeRepository);
    });

    List<Recipe> createTestRecipes() {
      return [
        Recipe(
          id: '1',
          name: 'Potato Agar Recipe',
          description: 'Basic agar recipe using potatoes',
          ingredients: [
            const Ingredient(name: 'Potato', measurement: '200g'),
            const Ingredient(name: 'Agar', measurement: '20g'),
          ],
          procedures: [
            const ProcedureStep(stepNumber: 1, instruction: 'Boil potatoes'),
            const ProcedureStep(stepNumber: 2, instruction: 'Add agar'),
          ],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
          successRate: 85.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Recipe(
          id: '2',
          name: 'Advanced Mushroom Culture',
          description: 'Complex mushroom cultivation recipe',
          ingredients: [
            const Ingredient(name: 'Mushroom Spawn', measurement: '100g'),
            const Ingredient(name: 'Sterilized Substrate', measurement: '500g'),
          ],
          procedures: [
            const ProcedureStep(stepNumber: 1, instruction: 'Prepare sterile environment'),
            const ProcedureStep(stepNumber: 2, instruction: 'Inoculate substrate'),
          ],
          difficultyLevel: 'advanced',
          estimatedTime: 168,
          isPremium: true,
          successRate: 75.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];
    }

    group('searchRecipes', () {
      test('should return all recipes when no filters applied', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.searchRecipes();

        // Assert
        expect(result, hasLength(2));
        expect(result, containsAll(testRecipes));
        verify(mockRecipeRepository.getRecipes()).called(1);
      });

      test('should filter by text query in name', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.searchRecipes(query: 'potato');

        // Assert
        expect(result, hasLength(1));
        expect(result.first.name, contains('Potato'));
      });

      test('should filter by text query in description', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.searchRecipes(query: 'complex');

        // Assert
        expect(result, hasLength(1));
        expect(result.first.description, contains('Complex'));
      });

      test('should filter by text query in ingredients', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.searchRecipes(query: 'mushroom');

        // Assert
        expect(result, hasLength(1));
        expect(result.first.ingredients.any((i) => i.name.toLowerCase().contains('mushroom')), isTrue);
      });

      test('should filter by difficulty level', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes(difficultyLevel: 'beginner'))
            .thenAnswer((_) async => [testRecipes.first]);

        // Act
        final result = await service.searchRecipes(difficultyLevel: 'beginner');

        // Assert
        expect(result, hasLength(1));
        expect(result.first.difficultyLevel, equals('beginner'));
      });

      test('should filter by premium status', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes(isPremium: true))
            .thenAnswer((_) async => [testRecipes.last]);

        // Act
        final result = await service.searchRecipes(isPremium: true);

        // Assert
        expect(result, hasLength(1));
        expect(result.first.isPremium, isTrue);
      });

      test('should filter by ingredients', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.searchRecipes(ingredients: ['potato']);

        // Assert
        expect(result, hasLength(1));
        expect(result.first.ingredients.any((i) => i.name.toLowerCase().contains('potato')), isTrue);
      });

      test('should filter by max time', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.searchRecipes(maxTime: 48);

        // Assert
        expect(result, hasLength(1));
        expect(result.first.estimatedTime, lessThanOrEqualTo(48));
      });

      test('should filter by min success rate', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.searchRecipes(minSuccessRate: 80.0);

        // Assert
        expect(result, hasLength(1));
        expect(result.first.successRate, greaterThanOrEqualTo(80.0));
      });

      test('should combine multiple filters', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes(difficultyLevel: 'beginner'))
            .thenAnswer((_) async => [testRecipes.first]);

        // Act
        final result = await service.searchRecipes(
          query: 'potato',
          difficultyLevel: 'beginner',
          maxTime: 48,
        );

        // Assert
        expect(result, hasLength(1));
        expect(result.first.name, contains('Potato'));
        expect(result.first.difficultyLevel, equals('beginner'));
        expect(result.first.estimatedTime, lessThanOrEqualTo(48));
      });
    });

    group('getSearchSuggestions', () {
      test('should return empty list for short queries', () async {
        // Act
        final result = await service.getSearchSuggestions('a');

        // Assert
        expect(result, isEmpty);
      });

      test('should return recipe name suggestions', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.getSearchSuggestions('pot');

        // Assert
        expect(result, contains('Potato Agar Recipe'));
      });

      test('should return ingredient suggestions', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.getSearchSuggestions('agar');

        // Assert
        expect(result, contains('Agar'));
      });

      test('should limit suggestions to 10', () async {
        // Arrange
        final manyRecipes = List.generate(15, (i) => Recipe(
          id: '$i',
          name: 'Recipe $i with potato',
          description: 'Description $i',
          ingredients: [Ingredient(name: 'Ingredient $i', measurement: '100g')],
          procedures: [ProcedureStep(stepNumber: 1, instruction: 'Step $i')],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
          successRate: 85.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => manyRecipes);

        // Act
        final result = await service.getSearchSuggestions('recipe');

        // Assert
        expect(result.length, lessThanOrEqualTo(10));
      });
    });

    group('getPopularIngredients', () {
      test('should return ingredients sorted by frequency', () async {
        // Arrange
        final testRecipes = [
          Recipe(
            id: '1',
            name: 'Recipe 1',
            description: 'Description',
            ingredients: [
              const Ingredient(name: 'potato', measurement: '100g'),
              const Ingredient(name: 'agar', measurement: '20g'),
            ],
            procedures: [const ProcedureStep(stepNumber: 1, instruction: 'Step')],
            difficultyLevel: 'beginner',
            estimatedTime: 24,
            isPremium: false,
            successRate: 85.0,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Recipe(
            id: '2',
            name: 'Recipe 2',
            description: 'Description',
            ingredients: [
              const Ingredient(name: 'potato', measurement: '200g'),
              const Ingredient(name: 'water', measurement: '500ml'),
            ],
            procedures: [const ProcedureStep(stepNumber: 1, instruction: 'Step')],
            difficultyLevel: 'beginner',
            estimatedTime: 24,
            isPremium: false,
            successRate: 85.0,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.getPopularIngredients();

        // Assert
        expect(result.first, equals('Potato')); // Most frequent
        expect(result, contains('Agar'));
        expect(result, contains('Water'));
      });

      test('should limit results to specified limit', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.getPopularIngredients(limit: 1);

        // Assert
        expect(result.length, lessThanOrEqualTo(1));
      });
    });

    group('getFilterStats', () {
      test('should return correct statistics', () async {
        // Arrange
        final testRecipes = createTestRecipes();
        when(mockRecipeRepository.getRecipes()).thenAnswer((_) async => testRecipes);

        // Act
        final result = await service.getFilterStats();

        // Assert
        expect(result['totalRecipes'], equals(2));
        expect(result['premiumRecipes'], equals(1));
        expect(result['freeRecipes'], equals(1));
        expect(result['difficultyStats']['beginner'], equals(1));
        expect(result['difficultyStats']['advanced'], equals(1));
        expect(result['averageTime'], equals(96)); // (24 + 168) / 2
        expect(result['averageSuccessRate'], equals(80)); // (85 + 75) / 2
      });
    });
  });
}
