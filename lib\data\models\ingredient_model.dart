import '../../domain/entities/ingredient.dart';

/// Ingredient model for data layer with JSON serialization
class IngredientModel extends Ingredient {
  const IngredientModel({
    required super.name,
    required super.measurement,
    super.notes,
  });

  /// Create IngredientModel from Map (JSON)
  factory IngredientModel.fromMap(Map<String, dynamic> map) {
    return IngredientModel(
      name: map['name'] as String,
      measurement: map['measurement'] as String,
      notes: map['notes'] as String?,
    );
  }

  /// Convert IngredientModel to Map for JSON storage
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'measurement': measurement,
      'notes': notes,
    };
  }

  /// Create IngredientModel from Ingredient entity
  factory IngredientModel.fromEntity(Ingredient ingredient) {
    return IngredientModel(
      name: ingredient.name,
      measurement: ingredient.measurement,
      notes: ingredient.notes,
    );
  }

  /// Convert to Ingredient entity
  Ingredient toEntity() {
    return Ingredient(
      name: name,
      measurement: measurement,
      notes: notes,
    );
  }

  @override
  IngredientModel copyWith({
    String? name,
    String? measurement,
    String? notes,
  }) {
    return IngredientModel(
      name: name ?? this.name,
      measurement: measurement ?? this.measurement,
      notes: notes ?? this.notes,
    );
  }
}
