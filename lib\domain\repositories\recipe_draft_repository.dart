import '../entities/recipe_draft.dart';

/// Repository interface for RecipeDraft data operations
///
/// [Source: Story 1.3 - Task 3: AC 4, 5, 7]
abstract class RecipeDraftRepository {
  /// Create a new recipe draft
  Future<void> createDraft(RecipeDraft draft);

  /// Get draft by ID
  Future<RecipeDraft?> getDraftById(String id);

  /// Get all drafts
  Future<List<RecipeDraft>> getAllDrafts();

  /// Update existing draft
  Future<void> updateDraft(RecipeDraft draft);

  /// Delete draft by ID
  Future<void> deleteDraft(String id);

  /// Delete all drafts (used when upgrading to premium)
  Future<void> deleteAllDrafts();

  /// Get drafts count
  Future<int> getDraftsCount();

  /// Check if draft exists
  Future<bool> draftExists(String id);
}
