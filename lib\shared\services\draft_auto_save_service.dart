import 'dart:async';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import '../../domain/entities/recipe_draft.dart';
import '../../domain/entities/ingredient.dart';
import '../../domain/entities/procedure_step.dart';
import '../../domain/repositories/recipe_draft_repository.dart';

/// Service for auto-saving recipe drafts when users hit limits
///
/// [Source: Story 1.3 - Task 3: AC 4, 5, 7]
class DraftAutoSaveService {
  final RecipeDraftRepository _draftRepository;
  final Uuid _uuid = const Uuid();
  
  Timer? _autoSaveTimer;
  RecipeDraft? _currentDraft;
  bool _hasUnsavedChanges = false;

  DraftAutoSaveService(this._draftRepository);

  /// Start auto-save for a new recipe draft
  void startAutoSave({
    String? draftId,
    required String name,
    required String description,
    required List<Ingredient> ingredients,
    required List<ProcedureStep> procedures,
    required String difficultyLevel,
    required int estimatedTime,
    required bool isPremium,
  }) {
    final now = DateTime.now();
    
    _currentDraft = RecipeDraft(
      id: draftId ?? _uuid.v4(),
      name: name,
      description: description,
      ingredients: ingredients,
      procedures: procedures,
      difficultyLevel: difficultyLevel,
      estimatedTime: estimatedTime,
      isPremium: isPremium,
      createdAt: now,
      updatedAt: now,
    );

    _hasUnsavedChanges = true;
    _startAutoSaveTimer();
  }

  /// Update the current draft with new data
  void updateDraft({
    String? name,
    String? description,
    List<Ingredient>? ingredients,
    List<ProcedureStep>? procedures,
    String? difficultyLevel,
    int? estimatedTime,
    bool? isPremium,
  }) {
    if (_currentDraft == null) return;

    _currentDraft = _currentDraft!.copyWith(
      name: name,
      description: description,
      ingredients: ingredients,
      procedures: procedures,
      difficultyLevel: difficultyLevel,
      estimatedTime: estimatedTime,
      isPremium: isPremium,
      updatedAt: DateTime.now(),
    );

    _hasUnsavedChanges = true;
  }

  /// Manually save the current draft
  Future<bool> saveDraft() async {
    if (_currentDraft == null || !_hasUnsavedChanges) {
      return false;
    }

    try {
      final exists = await _draftRepository.draftExists(_currentDraft!.id);
      
      if (exists) {
        await _draftRepository.updateDraft(_currentDraft!);
      } else {
        await _draftRepository.createDraft(_currentDraft!);
      }

      _hasUnsavedChanges = false;
      debugPrint('Draft saved successfully: ${_currentDraft!.id}');
      return true;
    } catch (e) {
      debugPrint('Failed to save draft: $e');
      return false;
    }
  }

  /// Save draft when user hits recipe limit
  Future<String?> saveDraftOnLimit({
    required String name,
    required String description,
    required List<Ingredient> ingredients,
    required List<ProcedureStep> procedures,
    required String difficultyLevel,
    required int estimatedTime,
    required bool isPremium,
  }) async {
    try {
      final now = DateTime.now();
      final draft = RecipeDraft(
        id: _uuid.v4(),
        name: name.isNotEmpty ? name : 'Untitled Recipe',
        description: description,
        ingredients: ingredients,
        procedures: procedures,
        difficultyLevel: difficultyLevel,
        estimatedTime: estimatedTime,
        isPremium: isPremium,
        createdAt: now,
        updatedAt: now,
      );

      await _draftRepository.createDraft(draft);
      debugPrint('Draft saved on limit: ${draft.id}');
      return draft.id;
    } catch (e) {
      debugPrint('Failed to save draft on limit: $e');
      return null;
    }
  }

  /// Load an existing draft
  Future<RecipeDraft?> loadDraft(String draftId) async {
    try {
      final draft = await _draftRepository.getDraftById(draftId);
      if (draft != null) {
        _currentDraft = draft;
        _hasUnsavedChanges = false;
        _startAutoSaveTimer();
      }
      return draft;
    } catch (e) {
      debugPrint('Failed to load draft: $e');
      return null;
    }
  }

  /// Delete a draft
  Future<bool> deleteDraft(String draftId) async {
    try {
      await _draftRepository.deleteDraft(draftId);
      
      // If this is the current draft, clear it
      if (_currentDraft?.id == draftId) {
        _currentDraft = null;
        _hasUnsavedChanges = false;
        _stopAutoSaveTimer();
      }
      
      return true;
    } catch (e) {
      debugPrint('Failed to delete draft: $e');
      return false;
    }
  }

  /// Get all drafts
  Future<List<RecipeDraft>> getAllDrafts() async {
    try {
      return await _draftRepository.getAllDrafts();
    } catch (e) {
      debugPrint('Failed to get all drafts: $e');
      return [];
    }
  }

  /// Stop auto-save and clear current draft
  void stopAutoSave() {
    _stopAutoSaveTimer();
    _currentDraft = null;
    _hasUnsavedChanges = false;
  }

  /// Check if there are unsaved changes
  bool get hasUnsavedChanges => _hasUnsavedChanges;

  /// Get current draft
  RecipeDraft? get currentDraft => _currentDraft;

  /// Start the auto-save timer (saves every 30 seconds)
  void _startAutoSaveTimer() {
    _stopAutoSaveTimer(); // Clear any existing timer
    
    _autoSaveTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) async {
        if (_hasUnsavedChanges) {
          await saveDraft();
        }
      },
    );
  }

  /// Stop the auto-save timer
  void _stopAutoSaveTimer() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = null;
  }

  /// Dispose resources
  void dispose() {
    _stopAutoSaveTimer();
  }

  /// Convert draft to recipe data for form restoration
  Map<String, dynamic> draftToFormData(RecipeDraft draft) {
    return {
      'id': draft.id,
      'name': draft.name,
      'description': draft.description,
      'ingredients': draft.ingredients,
      'procedures': draft.procedures,
      'difficultyLevel': draft.difficultyLevel,
      'estimatedTime': draft.estimatedTime,
      'isPremium': draft.isPremium,
    };
  }

  /// Show draft recovery dialog
  static Future<bool> showDraftRecoveryDialog(
    BuildContext context,
    List<RecipeDraft> drafts,
  ) async {
    if (drafts.isEmpty) return false;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.restore, color: Colors.orange),
            SizedBox(width: 8),
            Text('Recover Drafts'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'You have ${drafts.length} unsaved recipe draft${drafts.length == 1 ? '' : 's'}. Would you like to recover them?',
            ),
            const SizedBox(height: 16),
            ...drafts.take(3).map((draft) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Text(
                '• ${draft.name.isNotEmpty ? draft.name : 'Untitled Recipe'}',
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
              ),
            )),
            if (drafts.length > 3)
              Text(
                '• ... and ${drafts.length - 3} more',
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Not Now'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Recover Drafts'),
          ),
        ],
      ),
    );

    return result ?? false;
  }
}
