import 'ingredient.dart';
import 'procedure_step.dart';

/// RecipeDraft entity representing incomplete recipe drafts saved when hitting limits
///
/// [Source: Story 1.3 - AC 4, 5, 7]
class RecipeDraft {
  final String id;
  final String name;
  final String description;
  final List<Ingredient> ingredients;
  final List<ProcedureStep> procedures;
  final String difficultyLevel; // 'beginner', 'intermediate', 'advanced'
  final int estimatedTime; // minutes
  final bool isPremium;
  final DateTime createdAt;
  final DateTime updatedAt;

  const RecipeDraft({
    required this.id,
    required this.name,
    required this.description,
    required this.ingredients,
    required this.procedures,
    required this.difficultyLevel,
    required this.estimatedTime,
    required this.isPremium,
    required this.createdAt,
    required this.updatedAt,
  });

  RecipeDraft copyWith({
    String? id,
    String? name,
    String? description,
    List<Ingredient>? ingredients,
    List<ProcedureStep>? procedures,
    String? difficultyLevel,
    int? estimatedTime,
    bool? isPremium,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RecipeDraft(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ingredients: ingredients ?? this.ingredients,
      procedures: procedures ?? this.procedures,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      isPremium: isPremium ?? this.isPremium,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RecipeDraft && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'RecipeDraft(id: $id, name: $name, description: $description, difficultyLevel: $difficultyLevel, estimatedTime: $estimatedTime, isPremium: $isPremium, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
