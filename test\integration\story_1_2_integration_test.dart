import 'dart:io';

import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:culturestack/data/datasources/database_helper.dart';
import 'package:culturestack/data/repositories/recipe_repository_impl.dart';
import 'package:culturestack/domain/entities/recipe.dart';
import 'package:culturestack/domain/entities/ingredient.dart';
import 'package:culturestack/domain/entities/procedure_step.dart';
import 'package:uuid/uuid.dart';
import 'package:path/path.dart';
import '../test_helpers.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  setupPathProvider();
  group('Story 1.2 Integration Tests', () {
    late DatabaseHelper databaseHelper;
    late RecipeRepositoryImpl recipeRepository;
    const uuid = Uuid();
    String? dbPath;

    setUpAll(() {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() async {
      databaseHelper = DatabaseHelper();
      recipeRepository = RecipeRepositoryImpl(databaseHelper);
      await databaseHelper.database; // Initialize database
      dbPath = await getDatabasesPath();
    });

    tearDown(() async {
      // Clean up test database
      try {
        await databaseHelper.close();
        final dbFile = File(join(dbPath!, 'culturestack.db'));
        if (await dbFile.exists()) {
          await dbFile.delete();
        }
      } catch (e) {
        // Ignore cleanup errors
      }

      // Clean up backup file
      final backupFile = File(join(dbPath!, 'culturestack.db.bak'));
      if (await backupFile.exists()) {
        await backupFile.delete();
      }
    });

    test('AC3: Database backup is performed before critical operations',
        () async {
      final backupFile = File(join(dbPath!, 'culturestack.db.bak'));

      // 1. CREATE operation
      final recipe = Recipe(
        id: uuid.v4(),
        name: 'Backup Test Recipe',
        description: 'Testing backup functionality',
        ingredients: [
          const Ingredient(name: 'Test Ingredient', measurement: '100ml'),
        ],
        procedures: [
          const ProcedureStep(stepNumber: 1, instruction: 'Test step'),
        ],
        difficultyLevel: 'beginner',
        estimatedTime: 10,
        isPremium: false,
        successRate: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await recipeRepository.createRecipe(recipe);
      expect(await backupFile.exists(), isTrue,
          reason: 'Backup should be created after createRecipe');
      final createBackupStat = await backupFile.stat();

      // 2. UPDATE operation
      await Future.delayed(
          const Duration(milliseconds: 10)); // Ensure modification time changes
      final updatedRecipe = recipe.copyWith(name: 'Updated Backup Test');
      await recipeRepository.updateRecipe(updatedRecipe);
      final updateBackupStat = await backupFile.stat();
      expect(
          updateBackupStat.modified.isAfter(createBackupStat.modified), isTrue,
          reason: 'Backup should be updated after updateRecipe');

      // 3. DELETE operation
      await Future.delayed(
          const Duration(milliseconds: 10)); // Ensure modification time changes
      await recipeRepository.deleteRecipe(recipe.id);
      final deleteBackupStat = await backupFile.stat();
      expect(
          deleteBackupStat.modified.isAfter(updateBackupStat.modified), isTrue,
          reason: 'Backup should be updated after deleteRecipe');
    });
  });
}
