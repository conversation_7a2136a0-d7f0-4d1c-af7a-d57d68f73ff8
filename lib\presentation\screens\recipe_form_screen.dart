import 'package:flutter/material.dart';

import '../../domain/entities/recipe.dart';
import '../../domain/entities/recipe_draft.dart';
import '../widgets/recipe_form_widget.dart';

/// Screen for creating and editing recipes
///
/// [Source: Story 1.3 - Task 4: AC 1, 6]
class RecipeFormScreen extends StatelessWidget {
  final Recipe? existingRecipe;
  final RecipeDraft? existingDraft;
  final bool isDraft;
  final VoidCallback? onRecipeSaved;

  const RecipeFormScreen({
    super.key,
    this.existingRecipe,
    this.existingDraft,
    this.isDraft = false,
    this.onRecipeSaved,
  });

  @override
  Widget build(BuildContext context) {
    String title;
    if (existingRecipe != null) {
      title = 'Edit Recipe';
    } else if (existingDraft != null) {
      title = 'Edit Draft';
    } else if (isDraft) {
      title = 'Create Draft';
    } else {
      title = 'Create Recipe';
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ),
      body: RecipeFormWidget(
        existingRecipe: existingRecipe,
        existingDraft: existingDraft,
        isDraft: isDraft,
        onSaved: () {
          onRecipeSaved?.call();
          Navigator.of(context).pop();
        },
        onCancelled: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }
}
