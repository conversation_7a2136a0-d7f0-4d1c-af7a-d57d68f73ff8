import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../shared/services/recipe_search_service.dart';
import '../providers/core_providers.dart';

/// Advanced search widget with multiple filter options
///
/// [Source: Story 1.3 - Task 5: AC 6]
class AdvancedSearchWidget extends ConsumerStatefulWidget {
  final Function(Map<String, dynamic>) onFiltersChanged;
  final Map<String, dynamic>? initialFilters;

  const AdvancedSearchWidget({
    super.key,
    required this.onFiltersChanged,
    this.initialFilters,
  });

  @override
  ConsumerState<AdvancedSearchWidget> createState() => _AdvancedSearchWidgetState();
}

class _AdvancedSearchWidgetState extends ConsumerState<AdvancedSearchWidget> {
  final _searchController = TextEditingController();
  final _ingredientController = TextEditingController();
  
  String _selectedDifficulty = 'all';
  bool? _isPremium;
  double _maxTime = 168; // 1 week in hours
  double _minSuccessRate = 0;
  List<String> _selectedIngredients = [];
  List<String> _popularIngredients = [];
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadInitialFilters();
    _loadPopularIngredients();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _ingredientController.dispose();
    super.dispose();
  }

  void _loadInitialFilters() {
    if (widget.initialFilters != null) {
      final filters = widget.initialFilters!;
      _searchController.text = filters['query'] ?? '';
      _selectedDifficulty = filters['difficultyLevel'] ?? 'all';
      _isPremium = filters['isPremium'];
      _maxTime = (filters['maxTime'] ?? 168).toDouble();
      _minSuccessRate = (filters['minSuccessRate'] ?? 0).toDouble();
      _selectedIngredients = List<String>.from(filters['ingredients'] ?? []);
    }
  }

  Future<void> _loadPopularIngredients() async {
    try {
      final searchService = RecipeSearchService(ref.read(recipeRepositoryProvider));
      final ingredients = await searchService.getPopularIngredients(limit: 15);
      setState(() {
        _popularIngredients = ingredients;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  void _applyFilters() {
    final filters = <String, dynamic>{
      'query': _searchController.text.trim().isEmpty ? null : _searchController.text.trim(),
      'difficultyLevel': _selectedDifficulty == 'all' ? null : _selectedDifficulty,
      'isPremium': _isPremium,
      'ingredients': _selectedIngredients.isEmpty ? null : _selectedIngredients,
      'maxTime': _maxTime == 168 ? null : _maxTime.round(),
      'minSuccessRate': _minSuccessRate == 0 ? null : _minSuccessRate,
    };

    widget.onFiltersChanged(filters);
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedDifficulty = 'all';
      _isPremium = null;
      _maxTime = 168;
      _minSuccessRate = 0;
      _selectedIngredients.clear();
    });
    _applyFilters();
  }

  void _addIngredient(String ingredient) {
    if (ingredient.isNotEmpty && !_selectedIngredients.contains(ingredient)) {
      setState(() {
        _selectedIngredients.add(ingredient);
      });
      _ingredientController.clear();
      _applyFilters();
    }
  }

  void _removeIngredient(String ingredient) {
    setState(() {
      _selectedIngredients.remove(ingredient);
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search bar
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search recipes, ingredients, or procedures...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_searchController.text.isNotEmpty)
                      IconButton(
                        onPressed: () {
                          _searchController.clear();
                          _applyFilters();
                        },
                        icon: const Icon(Icons.clear),
                      ),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _isExpanded = !_isExpanded;
                        });
                      },
                      icon: Icon(_isExpanded ? Icons.expand_less : Icons.tune),
                    ),
                  ],
                ),
                border: const OutlineInputBorder(),
              ),
              onChanged: (_) => _applyFilters(),
            ),
            
            // Advanced filters (expandable)
            if (_isExpanded) ...[
              const SizedBox(height: 16),
              _buildAdvancedFilters(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Filter header with clear button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Advanced Filters',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: _clearFilters,
              child: const Text('Clear All'),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Difficulty and Premium filters
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedDifficulty,
                decoration: const InputDecoration(
                  labelText: 'Difficulty',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'all', child: Text('All Levels')),
                  DropdownMenuItem(value: 'beginner', child: Text('Beginner')),
                  DropdownMenuItem(value: 'intermediate', child: Text('Intermediate')),
                  DropdownMenuItem(value: 'advanced', child: Text('Advanced')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedDifficulty = value!;
                  });
                  _applyFilters();
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<bool?>(
                value: _isPremium,
                decoration: const InputDecoration(
                  labelText: 'Recipe Type',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: null, child: Text('All Recipes')),
                  DropdownMenuItem(value: false, child: Text('Free Only')),
                  DropdownMenuItem(value: true, child: Text('Premium Only')),
                ],
                onChanged: (value) {
                  setState(() {
                    _isPremium = value;
                  });
                  _applyFilters();
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Time filter
        Text('Max Time: ${_maxTime.round()} hours'),
        Slider(
          value: _maxTime,
          min: 1,
          max: 168,
          divisions: 20,
          label: '${_maxTime.round()}h',
          onChanged: (value) {
            setState(() {
              _maxTime = value;
            });
          },
          onChangeEnd: (_) => _applyFilters(),
        ),
        const SizedBox(height: 16),

        // Success rate filter
        Text('Min Success Rate: ${_minSuccessRate.round()}%'),
        Slider(
          value: _minSuccessRate,
          min: 0,
          max: 100,
          divisions: 20,
          label: '${_minSuccessRate.round()}%',
          onChanged: (value) {
            setState(() {
              _minSuccessRate = value;
            });
          },
          onChangeEnd: (_) => _applyFilters(),
        ),
        const SizedBox(height: 16),

        // Ingredient filter
        const Text(
          'Filter by Ingredients',
          style: TextStyle(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        
        // Selected ingredients
        if (_selectedIngredients.isNotEmpty) ...[
          Wrap(
            spacing: 8,
            children: _selectedIngredients.map((ingredient) {
              return Chip(
                label: Text(ingredient),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () => _removeIngredient(ingredient),
              );
            }).toList(),
          ),
          const SizedBox(height: 8),
        ],

        // Add ingredient field
        TextField(
          controller: _ingredientController,
          decoration: const InputDecoration(
            hintText: 'Add ingredient to filter...',
            border: OutlineInputBorder(),
            suffixIcon: Icon(Icons.add),
          ),
          onSubmitted: _addIngredient,
        ),
        const SizedBox(height: 8),

        // Popular ingredients
        if (_popularIngredients.isNotEmpty) ...[
          const Text(
            'Popular Ingredients:',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
          const SizedBox(height: 4),
          Wrap(
            spacing: 4,
            children: _popularIngredients.map((ingredient) {
              final isSelected = _selectedIngredients.contains(ingredient);
              return FilterChip(
                label: Text(ingredient),
                selected: isSelected,
                onSelected: (selected) {
                  if (selected) {
                    _addIngredient(ingredient);
                  } else {
                    _removeIngredient(ingredient);
                  }
                },
              );
            }).toList(),
          ),
        ],
      ],
    );
  }
}
