import 'dart:async';
import 'dart:io';
import 'package:meta/meta.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import '../../core/errors/exceptions.dart' as app_exceptions;
import '../../core/utils/logger.dart';

/// Database Helper for SQLite operations with mobile optimizations
///
/// [Source: architecture/database-schema.md]
class DatabaseHelper {
  static const String _databaseName = 'culturestack.db';
  static const int _databaseVersion = 4;

  Database? _database;
  Completer<Database> _dbOpenCompleter = Completer<Database>();

  DatabaseHelper();

  /// Get database instance with lazy initialization
  Future<Database> get database async {
    if (_database != null) return _database!;

    if (!_dbOpenCompleter.isCompleted) {
      _database = await _initDatabase();
      _dbOpenCompleter.complete(_database);
    }

    return _dbOpenCompleter.future;
  }

  /// Initialize database with mobile performance optimizations
  Future<Database> _initDatabase() async {
    try {
      final documentsDirectory = await getDatabasesPath();
      final path = join(documentsDirectory, _databaseName);

      final database = await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onOpen: _onOpen,
        onUpgrade: _onUpgrade,
      );

      return database;
    } catch (e) {
      await Logger().log('Failed to initialize database: ${e.toString()}');
      throw app_exceptions.DatabaseInitializationException(
        'Failed to initialize database: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Configure mobile performance optimizations on database open
  Future<void> _onOpen(Database db) async {
    try {
      // Enable mobile performance optimizations
      // [Source: architecture/database-schema.md]

      // Try WAL mode first, fallback to DELETE mode if not supported
      try {
        await db.execute('PRAGMA journal_mode = WAL');
      } catch (e) {
        // WAL mode not supported, try DELETE mode
        try {
          await db.execute('PRAGMA journal_mode = DELETE');
          try {
            await Logger().log(
                'WAL mode not supported, using DELETE mode: ${e.toString()}');
          } catch (_) {
            // Ignore logging errors during tests
          }
        } catch (deleteError) {
          // Both WAL and DELETE modes failed, continue without journal mode setting
          try {
            await Logger().log(
                'Failed to configure database performance settings: ${deleteError.toString()}');
          } catch (_) {
            // Ignore logging errors during tests
          }
        }
      }

      // Apply other performance settings with individual error handling
      try {
        await db.execute('PRAGMA synchronous = NORMAL');
      } catch (e) {
        try {
          await Logger().log('Failed to set synchronous mode: ${e.toString()}');
        } catch (_) {}
      }

      try {
        await db.execute('PRAGMA cache_size = 10000');
      } catch (e) {
        try {
          await Logger().log('Failed to set cache size: ${e.toString()}');
        } catch (_) {}
      }

      try {
        await db.execute('PRAGMA foreign_keys = ON');
      } catch (e) {
        try {
          await Logger().log('Failed to enable foreign keys: ${e.toString()}');
        } catch (_) {}
      }

      try {
        await db.execute('PRAGMA temp_store = MEMORY');
      } catch (e) {
        try {
          await Logger().log('Failed to set temp store: ${e.toString()}');
        } catch (_) {}
      }

      // Additional mobile optimizations for older devices
      // [Source: Mobile-Optimized SQLite Performance Settings memory]
      try {
        await db
            .execute('PRAGMA mmap_size = 268435456'); // 256MB memory-mapped I/O
      } catch (e) {
        try {
          await Logger().log('Failed to set mmap size: ${e.toString()}');
        } catch (_) {}
      }

      try {
        await db.execute('PRAGMA optimize');
      } catch (e) {
        try {
          await Logger().log('Failed to optimize database: ${e.toString()}');
        } catch (_) {}
      }
    } catch (e) {
      await Logger().log(
          'Failed to configure database performance settings: ${e.toString()}');
      throw app_exceptions.DatabaseException(
        'Failed to configure database performance settings: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Create database schema
  Future<void> _onCreate(Database db, int version) async {
    try {
      await _createTables(db);
      await _createIndexes(db);
      await _createTriggers(db);
    } catch (e) {
      await Logger().log('Failed to create database schema: ${e.toString()}');
      throw app_exceptions.DatabaseException(
        'Failed to create database schema: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Handle database upgrades for future migrations
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Future migration logic will be added here
    // For now, recreate the database
    await _dropTables(db);
    await _onCreate(db, newVersion);
  }

  /// Create core tables
  Future<void> _createTables(Database db) async {
    // Recipes table
    await db.execute('''
      CREATE TABLE recipes (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        ingredients TEXT, -- JSON array of ingredients
        procedures TEXT, -- JSON array of procedure steps
        difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
        estimated_time INTEGER,
        is_premium BOOLEAN DEFAULT FALSE,
        success_rate REAL DEFAULT 0.0,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL
      )
    ''');

    // Recipe drafts table
    await db.execute('''
      CREATE TABLE recipe_drafts (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        ingredients TEXT, -- JSON array of ingredients
        procedures TEXT, -- JSON array of procedure steps
        difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
        estimated_time INTEGER,
        is_premium BOOLEAN DEFAULT FALSE,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL
      )
    ''');

    // Cultures table
    await db.execute('''
      CREATE TABLE cultures (
        id TEXT PRIMARY KEY,
        recipe_id TEXT NOT NULL,
        batch_name TEXT NOT NULL,
        start_date DATETIME NOT NULL,
        outcome TEXT CHECK (outcome IN ('success', 'failure', 'partial_success', 'in_progress')),
        temperature REAL,
        humidity REAL,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE
      )
    ''');

    // Culture photos table
    await db.execute('''
      CREATE TABLE culture_photos (
        id TEXT PRIMARY KEY,
        culture_id TEXT NOT NULL,
        file_path TEXT NOT NULL,
        capture_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        stage TEXT CHECK (stage IN ('initial', 'progress', 'final')),
        compression_quality INTEGER DEFAULT 85,
        notes TEXT,
        FOREIGN KEY (culture_id) REFERENCES cultures(id) ON DELETE CASCADE
      )
    ''');

    // Premium status table
    await db.execute('''
      CREATE TABLE premium_status (
        user_device_id TEXT PRIMARY KEY,
        is_premium BOOLEAN DEFAULT FALSE,
        purchase_token TEXT,
        purchase_date DATETIME,
        purchase_platform TEXT CHECK (purchase_platform IN ('google_play', 'apple_app_store')),
        last_verified DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    ''');
  }

  /// Create performance indexes
  Future<void> _createIndexes(Database db) async {
    await db.execute(
        'CREATE INDEX idx_recipes_created_at ON recipes(created_at DESC)');
    await db
        .execute('CREATE INDEX idx_cultures_recipe_id ON cultures(recipe_id)');
    await db.execute(
        'CREATE INDEX idx_culture_photos_culture_id ON culture_photos(culture_id)');
    await db.execute('CREATE INDEX idx_recipes_premium ON recipes(is_premium)');
    await db.execute('CREATE INDEX idx_cultures_outcome ON cultures(outcome)');
  }

  /// Create data integrity triggers
  Future<void> _createTriggers(Database db) async {
    // Success rate calculation trigger
    await db.execute('''
      CREATE TRIGGER update_recipe_success_rate
      AFTER INSERT ON cultures
      BEGIN
        UPDATE recipes 
        SET success_rate = (
          SELECT ROUND(
            (COUNT(CASE WHEN outcome = 'success' THEN 1 END) * 100.0) / COUNT(*), 2
          )
          FROM cultures 
          WHERE recipe_id = NEW.recipe_id 
          AND outcome IN ('success', 'failure', 'partial_success')
        ),
        updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.recipe_id;
      END
    ''');

    // Update trigger for recipes
    await db.execute('''
      CREATE TRIGGER update_recipe_timestamp
      AFTER UPDATE ON recipes
      BEGIN
        UPDATE recipes SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    ''');
  }

  /// Drop all tables (for migrations)
  Future<void> _dropTables(Database db) async {
    await db.execute('DROP TABLE IF EXISTS culture_photos');
    await db.execute('DROP TABLE IF EXISTS cultures');
    await db.execute('DROP TABLE IF EXISTS recipe_drafts');
    await db.execute('DROP TABLE IF EXISTS recipes');
    await db.execute('DROP TABLE IF EXISTS premium_status');
  }

  /// Check database health and detect corruption
  Future<bool> checkDatabaseHealth() async {
    try {
      final db = await database;
      final result = await db.rawQuery('PRAGMA integrity_check');
      return result.isNotEmpty && result.first['integrity_check'] == 'ok';
    } catch (e) {
      try {
        await Logger().log('Database health check failed: ${e.toString()}');
      } catch (_) {
        // Ignore logging errors during tests
      }
      throw app_exceptions.DatabaseCorruptionException(
        'Database health check failed: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Execute a database transaction with retry logic for lock conflicts
  Future<T> transaction<T>(Future<T> Function(Transaction txn) action) async {
    final db = await database;
    int retries = 5;
    int delay = 100;

    for (int i = 0; i < retries; i++) {
      try {
        return await db.transaction(action);
      } catch (e) {
        final errorMessage = e.toString();
        if (errorMessage.contains('database is locked') ||
            errorMessage.contains('SQLITE_BUSY')) {
          if (i == retries - 1) {
            try {
              await Logger().log(
                  'Database transaction failed after $retries retries due to lock: ${e.toString()}');
            } catch (_) {
              // Ignore logging errors during tests
            }
            throw app_exceptions.DatabaseException(
              'Database transaction failed after $retries retries due to lock.',
              originalError: e,
            );
          }
          await Future.delayed(Duration(milliseconds: delay));
          delay *= 2; // Exponential backoff
        } else {
          try {
            await Logger().log('Database transaction failed: ${e.toString()}');
          } catch (_) {
            // Ignore logging errors during tests
          }
          throw app_exceptions.DatabaseException(
            'Database transaction failed: ${e.toString()}',
            originalError: e,
          );
        }
      }
    }
    // This should not be reached
    throw app_exceptions.DatabaseException(
        'Database transaction failed unexpectedly.');
  }

  /// Backup the database
  Future<void> backupDatabase() async {
    try {
      await Logger().log('Starting database backup...');
      final dbPath = await getDatabasesPath();
      final dbFile = join(dbPath, _databaseName);
      final backupFile = File(join(dbPath, '$_databaseName.bak'));

      // Ensure the database exists before attempting backup
      final sourceFile = File(dbFile);
      if (!await sourceFile.exists()) {
        // Initialize database if it doesn't exist
        await database; // This will create the database
      }

      if (await backupFile.exists()) {
        await backupFile.delete();
      }
      await sourceFile.copy(backupFile.path);
      await Logger().log('Database backup completed successfully.');
    } catch (e) {
      try {
        await Logger()
            .log('Database backup failed (non-critical): ${e.toString()}');
      } catch (_) {
        // Ignore logging errors during tests
      }
      // Don't throw exception for backup failures - they shouldn't block main operations
    }
  }

  /// Restore the database from a backup
  Future<void> restoreDatabase() async {
    try {
      final dbPath = await getDatabasesPath();
      final dbFile = join(dbPath, _databaseName);
      final backupFile = join(dbPath, '$_databaseName.bak');

      await close(); // Close the database before restoring
      await File(backupFile).copy(dbFile);
    } catch (e) {
      try {
        await Logger().log('Failed to restore database: ${e.toString()}');
      } catch (_) {
        // Ignore logging errors during tests
      }
      throw app_exceptions.DatabaseException(
        'Failed to restore database: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Close database connection
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      _dbOpenCompleter = Completer<Database>(); // Reset the completer
    }
  }

  /// FOR TESTING ONLY
  @visibleForTesting
  void setDatabase(Database db) {
    _database = db;
  }
}
