import 'dart:convert';

import '../../domain/entities/recipe_draft.dart';
import '../../domain/entities/ingredient.dart';
import '../../domain/entities/procedure_step.dart';
import 'ingredient_model.dart';
import 'procedure_step_model.dart';

/// RecipeDraft model for data layer with SQLite serialization
///
/// [Source: Story 1.3 - Task 3]
class RecipeDraftModel extends RecipeDraft {
  const RecipeDraftModel({
    required super.id,
    required super.name,
    required super.description,
    required super.ingredients,
    required super.procedures,
    required super.difficultyLevel,
    required super.estimatedTime,
    required super.isPremium,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Create RecipeDraftModel from Map (SQLite row)
  factory RecipeDraftModel.fromMap(Map<String, dynamic> map) {
    // Parse ingredients from JSON
    List<Ingredient> ingredients = [];
    if (map['ingredients'] != null) {
      final ingredientsJson = jsonDecode(map['ingredients'] as String) as List;
      ingredients = ingredientsJson
          .map((json) =>
              IngredientModel.fromMap(json as Map<String, dynamic>).toEntity())
          .toList();
    }

    // Parse procedures from JSON
    List<ProcedureStep> procedures = [];
    if (map['procedures'] != null) {
      final proceduresJson = jsonDecode(map['procedures'] as String) as List;
      procedures = proceduresJson
          .map((json) =>
              ProcedureStepModel.fromMap(json as Map<String, dynamic>)
                  .toEntity())
          .toList();
    }

    return RecipeDraftModel(
      id: map['id'] as String,
      name: map['name'] as String,
      description: map['description'] as String? ?? '',
      ingredients: ingredients,
      procedures: procedures,
      difficultyLevel: map['difficulty_level'] as String,
      estimatedTime: map['estimated_time'] as int,
      isPremium: (map['is_premium'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String).toLocal(),
      updatedAt: DateTime.parse(map['updated_at'] as String).toLocal(),
    );
  }

  /// Convert RecipeDraftModel to Map for SQLite storage
  Map<String, dynamic> toMap() {
    // Serialize ingredients to JSON
    final ingredientsJson = jsonEncode(
      ingredients
          .map((ingredient) => IngredientModel.fromEntity(ingredient).toMap())
          .toList(),
    );

    // Serialize procedures to JSON
    final proceduresJson = jsonEncode(
      procedures
          .map((procedure) => ProcedureStepModel.fromEntity(procedure).toMap())
          .toList(),
    );

    return {
      'id': id,
      'name': name,
      'description': description,
      'ingredients': ingredientsJson,
      'procedures': proceduresJson,
      'difficulty_level': difficultyLevel,
      'estimated_time': estimatedTime,
      'is_premium': isPremium ? 1 : 0,
      'created_at': createdAt.toUtc().toIso8601String(),
      'updated_at': updatedAt.toUtc().toIso8601String(),
    };
  }

  /// Create RecipeDraftModel from RecipeDraft entity
  factory RecipeDraftModel.fromEntity(RecipeDraft draft) {
    return RecipeDraftModel(
      id: draft.id,
      name: draft.name,
      description: draft.description,
      ingredients: draft.ingredients,
      procedures: draft.procedures,
      difficultyLevel: draft.difficultyLevel,
      estimatedTime: draft.estimatedTime,
      isPremium: draft.isPremium,
      createdAt: draft.createdAt,
      updatedAt: draft.updatedAt,
    );
  }

  /// Convert to RecipeDraft entity
  RecipeDraft toEntity() {
    return RecipeDraft(
      id: id,
      name: name,
      description: description,
      ingredients: ingredients,
      procedures: procedures,
      difficultyLevel: difficultyLevel,
      estimatedTime: estimatedTime,
      isPremium: isPremium,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  @override
  RecipeDraftModel copyWith({
    String? id,
    String? name,
    String? description,
    List<Ingredient>? ingredients,
    List<ProcedureStep>? procedures,
    String? difficultyLevel,
    int? estimatedTime,
    bool? isPremium,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RecipeDraftModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ingredients: ingredients ?? this.ingredients,
      procedures: procedures ?? this.procedures,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      isPremium: isPremium ?? this.isPremium,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
