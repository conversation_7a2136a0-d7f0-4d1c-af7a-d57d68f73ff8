import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:culturestack/shared/services/draft_auto_save_service.dart';
import 'package:culturestack/domain/repositories/recipe_draft_repository.dart';
import 'package:culturestack/domain/entities/recipe_draft.dart';
import 'package:culturestack/domain/entities/ingredient.dart';
import 'package:culturestack/domain/entities/procedure_step.dart';

import 'draft_auto_save_service_test.mocks.dart';

@GenerateMocks([RecipeDraftRepository])
void main() {
  group('DraftAutoSaveService', () {
    late DraftAutoSaveService service;
    late MockRecipeDraftRepository mockDraftRepository;

    setUp(() {
      mockDraftRepository = MockRecipeDraftRepository();
      service = DraftAutoSaveService(mockDraftRepository);
    });

    tearDown(() {
      service.dispose();
    });

    RecipeDraft createTestDraft() {
      return RecipeDraft(
        id: 'test-draft-id',
        name: 'Test Draft',
        description: 'Test description',
        ingredients: [
          const Ingredient(name: 'Test Ingredient', measurement: '100g'),
        ],
        procedures: [
          const ProcedureStep(stepNumber: 1, instruction: 'Test step'),
        ],
        difficultyLevel: 'beginner',
        estimatedTime: 24,
        isPremium: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }

    group('startAutoSave', () {
      test('should create new draft with provided data', () {
        // Act
        service.startAutoSave(
          name: 'Test Recipe',
          description: 'Test description',
          ingredients: [const Ingredient(name: 'Test', measurement: '100g')],
          procedures: [const ProcedureStep(stepNumber: 1, instruction: 'Test')],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
        );

        // Assert
        expect(service.currentDraft, isNotNull);
        expect(service.currentDraft!.name, equals('Test Recipe'));
        expect(service.hasUnsavedChanges, isTrue);
      });

      test('should use provided draft ID if given', () {
        // Arrange
        const testId = 'custom-draft-id';

        // Act
        service.startAutoSave(
          draftId: testId,
          name: 'Test Recipe',
          description: 'Test description',
          ingredients: [],
          procedures: [],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
        );

        // Assert
        expect(service.currentDraft!.id, equals(testId));
      });
    });

    group('updateDraft', () {
      test('should update current draft with new data', () {
        // Arrange
        service.startAutoSave(
          name: 'Original Name',
          description: 'Original description',
          ingredients: [],
          procedures: [],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
        );

        // Act
        service.updateDraft(
          name: 'Updated Name',
          description: 'Updated description',
        );

        // Assert
        expect(service.currentDraft!.name, equals('Updated Name'));
        expect(service.currentDraft!.description, equals('Updated description'));
        expect(service.hasUnsavedChanges, isTrue);
      });

      test('should not update if no current draft', () {
        // Act
        service.updateDraft(name: 'Test');

        // Assert
        expect(service.currentDraft, isNull);
      });
    });

    group('saveDraft', () {
      test('should save new draft to repository', () async {
        // Arrange
        service.startAutoSave(
          name: 'Test Recipe',
          description: 'Test description',
          ingredients: [],
          procedures: [],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
        );
        when(mockDraftRepository.draftExists(any)).thenAnswer((_) async => false);
        when(mockDraftRepository.createDraft(any)).thenAnswer((_) async {});

        // Act
        final result = await service.saveDraft();

        // Assert
        expect(result, isTrue);
        expect(service.hasUnsavedChanges, isFalse);
        verify(mockDraftRepository.draftExists(any)).called(1);
        verify(mockDraftRepository.createDraft(any)).called(1);
      });

      test('should update existing draft in repository', () async {
        // Arrange
        service.startAutoSave(
          name: 'Test Recipe',
          description: 'Test description',
          ingredients: [],
          procedures: [],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
        );
        when(mockDraftRepository.draftExists(any)).thenAnswer((_) async => true);
        when(mockDraftRepository.updateDraft(any)).thenAnswer((_) async {});

        // Act
        final result = await service.saveDraft();

        // Assert
        expect(result, isTrue);
        expect(service.hasUnsavedChanges, isFalse);
        verify(mockDraftRepository.draftExists(any)).called(1);
        verify(mockDraftRepository.updateDraft(any)).called(1);
      });

      test('should return false if no current draft', () async {
        // Act
        final result = await service.saveDraft();

        // Assert
        expect(result, isFalse);
        verifyNever(mockDraftRepository.createDraft(any));
        verifyNever(mockDraftRepository.updateDraft(any));
      });

      test('should return false if no unsaved changes', () async {
        // Arrange
        service.startAutoSave(
          name: 'Test Recipe',
          description: 'Test description',
          ingredients: [],
          procedures: [],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
        );
        // Simulate already saved
        when(mockDraftRepository.draftExists(any)).thenAnswer((_) async => false);
        when(mockDraftRepository.createDraft(any)).thenAnswer((_) async {});
        await service.saveDraft(); // First save

        // Act
        final result = await service.saveDraft(); // Second save

        // Assert
        expect(result, isFalse);
        verify(mockDraftRepository.createDraft(any)).called(1); // Only called once
      });
    });

    group('saveDraftOnLimit', () {
      test('should create and save draft with provided data', () async {
        // Arrange
        when(mockDraftRepository.createDraft(any)).thenAnswer((_) async {});

        // Act
        final result = await service.saveDraftOnLimit(
          name: 'Limit Recipe',
          description: 'Saved on limit',
          ingredients: [const Ingredient(name: 'Test', measurement: '100g')],
          procedures: [const ProcedureStep(stepNumber: 1, instruction: 'Test')],
          difficultyLevel: 'intermediate',
          estimatedTime: 48,
          isPremium: true,
        );

        // Assert
        expect(result, isNotNull);
        verify(mockDraftRepository.createDraft(any)).called(1);
      });

      test('should use default name if empty name provided', () async {
        // Arrange
        when(mockDraftRepository.createDraft(any)).thenAnswer((_) async {});

        // Act
        final result = await service.saveDraftOnLimit(
          name: '',
          description: 'Test',
          ingredients: [],
          procedures: [],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
        );

        // Assert
        expect(result, isNotNull);
        final capturedDraft = verify(mockDraftRepository.createDraft(captureAny)).captured.single as RecipeDraft;
        expect(capturedDraft.name, equals('Untitled Recipe'));
      });

      test('should return null on repository error', () async {
        // Arrange
        when(mockDraftRepository.createDraft(any)).thenThrow(Exception('Repository error'));

        // Act
        final result = await service.saveDraftOnLimit(
          name: 'Test',
          description: 'Test',
          ingredients: [],
          procedures: [],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
        );

        // Assert
        expect(result, isNull);
      });
    });

    group('loadDraft', () {
      test('should load draft from repository and set as current', () async {
        // Arrange
        final testDraft = createTestDraft();
        when(mockDraftRepository.getDraftById('test-id')).thenAnswer((_) async => testDraft);

        // Act
        final result = await service.loadDraft('test-id');

        // Assert
        expect(result, equals(testDraft));
        expect(service.currentDraft, equals(testDraft));
        expect(service.hasUnsavedChanges, isFalse);
        verify(mockDraftRepository.getDraftById('test-id')).called(1);
      });

      test('should return null if draft not found', () async {
        // Arrange
        when(mockDraftRepository.getDraftById('non-existent')).thenAnswer((_) async => null);

        // Act
        final result = await service.loadDraft('non-existent');

        // Assert
        expect(result, isNull);
        expect(service.currentDraft, isNull);
      });
    });

    group('deleteDraft', () {
      test('should delete draft from repository', () async {
        // Arrange
        when(mockDraftRepository.deleteDraft('test-id')).thenAnswer((_) async {});

        // Act
        final result = await service.deleteDraft('test-id');

        // Assert
        expect(result, isTrue);
        verify(mockDraftRepository.deleteDraft('test-id')).called(1);
      });

      test('should clear current draft if deleting current draft', () async {
        // Arrange
        service.startAutoSave(
          draftId: 'test-id',
          name: 'Test',
          description: 'Test',
          ingredients: [],
          procedures: [],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
        );
        when(mockDraftRepository.deleteDraft('test-id')).thenAnswer((_) async {});

        // Act
        final result = await service.deleteDraft('test-id');

        // Assert
        expect(result, isTrue);
        expect(service.currentDraft, isNull);
        expect(service.hasUnsavedChanges, isFalse);
      });

      test('should return false on repository error', () async {
        // Arrange
        when(mockDraftRepository.deleteDraft('test-id')).thenThrow(Exception('Delete error'));

        // Act
        final result = await service.deleteDraft('test-id');

        // Assert
        expect(result, isFalse);
      });
    });

    group('getAllDrafts', () {
      test('should return all drafts from repository', () async {
        // Arrange
        final testDrafts = [createTestDraft()];
        when(mockDraftRepository.getAllDrafts()).thenAnswer((_) async => testDrafts);

        // Act
        final result = await service.getAllDrafts();

        // Assert
        expect(result, equals(testDrafts));
        verify(mockDraftRepository.getAllDrafts()).called(1);
      });

      test('should return empty list on repository error', () async {
        // Arrange
        when(mockDraftRepository.getAllDrafts()).thenThrow(Exception('Get error'));

        // Act
        final result = await service.getAllDrafts();

        // Assert
        expect(result, isEmpty);
      });
    });

    group('stopAutoSave', () {
      test('should clear current draft and unsaved changes', () {
        // Arrange
        service.startAutoSave(
          name: 'Test',
          description: 'Test',
          ingredients: [],
          procedures: [],
          difficultyLevel: 'beginner',
          estimatedTime: 24,
          isPremium: false,
        );

        // Act
        service.stopAutoSave();

        // Assert
        expect(service.currentDraft, isNull);
        expect(service.hasUnsavedChanges, isFalse);
      });
    });

    group('draftToFormData', () {
      test('should convert draft to form data map', () {
        // Arrange
        final testDraft = createTestDraft();

        // Act
        final result = service.draftToFormData(testDraft);

        // Assert
        expect(result['id'], equals(testDraft.id));
        expect(result['name'], equals(testDraft.name));
        expect(result['description'], equals(testDraft.description));
        expect(result['ingredients'], equals(testDraft.ingredients));
        expect(result['procedures'], equals(testDraft.procedures));
        expect(result['difficultyLevel'], equals(testDraft.difficultyLevel));
        expect(result['estimatedTime'], equals(testDraft.estimatedTime));
        expect(result['isPremium'], equals(testDraft.isPremium));
      });
    });
  });
}
