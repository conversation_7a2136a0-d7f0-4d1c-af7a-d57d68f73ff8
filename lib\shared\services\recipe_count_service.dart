import '../../domain/repositories/recipe_repository.dart';
import '../../domain/repositories/premium_repository.dart';

/// Service for managing recipe count limits and premium integration
///
/// [Source: Story 1.3 - Task 2: AC 2, 3, 4, 7]
class RecipeCountService {
  final RecipeRepository _recipeRepository;
  final PremiumRepository _premiumRepository;

  // Free tier limit: 10 recipes
  static const int freeRecipeLimit = 10;
  // Warning threshold: 8/10 recipes
  static const int warningThreshold = 8;

  RecipeCountService(this._recipeRepository, this._premiumRepository);

  /// Get current recipe count for user
  Future<int> getCurrentRecipeCount() async {
    return await _recipeRepository.getRecipesCount();
  }

  /// Check if user can create a new recipe
  Future<bool> canCreateRecipe(String userDeviceId) async {
    final isPremium = await _premiumRepository.isPremiumUser(userDeviceId);
    
    // Premium users have unlimited recipes
    if (isPremium) {
      return true;
    }

    // Free users are limited to 10 recipes
    final currentCount = await getCurrentRecipeCount();
    return currentCount < freeRecipeLimit;
  }

  /// Check if user should see warning notification (8/10 recipes)
  Future<bool> shouldShowWarning(String userDeviceId) async {
    final isPremium = await _premiumRepository.isPremiumUser(userDeviceId);
    
    // Premium users don't see warnings
    if (isPremium) {
      return false;
    }

    final currentCount = await getCurrentRecipeCount();
    return currentCount >= warningThreshold && currentCount < freeRecipeLimit;
  }

  /// Check if user has reached the limit (10/10 recipes)
  Future<bool> hasReachedLimit(String userDeviceId) async {
    final isPremium = await _premiumRepository.isPremiumUser(userDeviceId);
    
    // Premium users never reach limit
    if (isPremium) {
      return false;
    }

    final currentCount = await getCurrentRecipeCount();
    return currentCount >= freeRecipeLimit;
  }

  /// Get usage indicator text (e.g., "7/10 recipes used")
  Future<String> getUsageIndicatorText(String userDeviceId) async {
    final isPremium = await _premiumRepository.isPremiumUser(userDeviceId);
    
    if (isPremium) {
      final currentCount = await getCurrentRecipeCount();
      return '$currentCount recipes (Premium)';
    }

    final currentCount = await getCurrentRecipeCount();
    return '$currentCount/$freeRecipeLimit recipes used';
  }

  /// Get remaining recipe slots for free users
  Future<int> getRemainingSlots(String userDeviceId) async {
    final isPremium = await _premiumRepository.isPremiumUser(userDeviceId);
    
    // Premium users have unlimited slots
    if (isPremium) {
      return -1; // Indicates unlimited
    }

    final currentCount = await getCurrentRecipeCount();
    return (freeRecipeLimit - currentCount).clamp(0, freeRecipeLimit);
  }

  /// Check if user needs upgrade prompt
  Future<bool> needsUpgradePrompt(String userDeviceId) async {
    return await hasReachedLimit(userDeviceId);
  }
}
