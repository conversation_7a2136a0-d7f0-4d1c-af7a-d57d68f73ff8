import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';

import '../../domain/entities/recipe.dart';
import '../../domain/entities/recipe_draft.dart';
import '../../domain/entities/ingredient.dart';
import '../../domain/entities/procedure_step.dart';
import '../providers/core_providers.dart';
import '../../shared/services/draft_auto_save_service.dart';

/// Widget for creating and editing recipes with ingredients and procedures
///
/// [Source: Story 1.3 - Task 4: AC 1, 6]
class RecipeFormWidget extends ConsumerStatefulWidget {
  final Recipe? existingRecipe;
  final RecipeDraft? existingDraft;
  final bool isDraft;
  final VoidCallback? onSaved;
  final VoidCallback? onCancelled;

  const RecipeFormWidget({
    super.key,
    this.existingRecipe,
    this.existingDraft,
    this.isDraft = false,
    this.onSaved,
    this.onCancelled,
  });

  @override
  ConsumerState<RecipeFormWidget> createState() => _RecipeFormWidgetState();
}

class _RecipeFormWidgetState extends ConsumerState<RecipeFormWidget> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _estimatedTimeController = TextEditingController();

  String _selectedDifficulty = 'beginner';
  bool _isPremium = false;
  double _successRate = 85.0;

  List<Ingredient> _ingredients = [];
  List<ProcedureStep> _procedures = [];

  bool _isLoading = false;
  DraftAutoSaveService? _draftService;

  @override
  void initState() {
    super.initState();
    _initializeForm();

    if (widget.isDraft) {
      _draftService = ref.read(draftAutoSaveServiceProvider);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _estimatedTimeController.dispose();
    _draftService?.dispose();
    super.dispose();
  }

  void _initializeForm() {
    if (widget.existingRecipe != null) {
      final recipe = widget.existingRecipe!;
      _nameController.text = recipe.name;
      _descriptionController.text = recipe.description;
      _estimatedTimeController.text = recipe.estimatedTime.toString();
      _selectedDifficulty = recipe.difficultyLevel;
      _isPremium = recipe.isPremium;
      _successRate = recipe.successRate;
      _ingredients = List.from(recipe.ingredients);
      _procedures = List.from(recipe.procedures);
    } else if (widget.existingDraft != null) {
      final draft = widget.existingDraft!;
      _nameController.text = draft.name;
      _descriptionController.text = draft.description;
      _estimatedTimeController.text = draft.estimatedTime.toString();
      _selectedDifficulty = draft.difficultyLevel;
      _isPremium = draft.isPremium;
      _ingredients = List.from(draft.ingredients);
      _procedures = List.from(draft.procedures);
    } else {
      // Default values for new recipe
      _estimatedTimeController.text = '24';
      _ingredients = [const Ingredient(name: '', measurement: '')];
      _procedures = [const ProcedureStep(stepNumber: 1, instruction: '')];
    }
  }

  Future<void> _saveRecipe() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final recipe = Recipe(
        id: widget.existingRecipe?.id ?? const Uuid().v4(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        ingredients: _ingredients.where((i) => i.name.isNotEmpty).toList(),
        procedures: _procedures.where((p) => p.instruction.isNotEmpty).toList(),
        difficultyLevel: _selectedDifficulty,
        estimatedTime: int.parse(_estimatedTimeController.text),
        isPremium: _isPremium,
        successRate: _successRate,
        createdAt: widget.existingRecipe?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final recipeRepository = ref.read(recipeRepositoryProvider);

      if (widget.existingRecipe != null) {
        await recipeRepository.updateRecipe(recipe);
      } else {
        await recipeRepository.createRecipe(recipe);
      }

      // If this was created from a draft, delete the draft
      if (widget.existingDraft != null) {
        final draftRepository = ref.read(recipeDraftRepositoryProvider);
        await draftRepository.deleteDraft(widget.existingDraft!.id);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.existingRecipe != null
                ? 'Recipe updated successfully!'
                : 'Recipe created successfully!'),
          ),
        );
        widget.onSaved?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to save recipe: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _saveDraft() async {
    if (widget.isDraft && _draftService != null) {
      final draftId = await _draftService!.saveDraftOnLimit(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        ingredients: _ingredients.where((i) => i.name.isNotEmpty).toList(),
        procedures: _procedures.where((p) => p.instruction.isNotEmpty).toList(),
        difficultyLevel: _selectedDifficulty,
        estimatedTime: int.tryParse(_estimatedTimeController.text) ?? 24,
        isPremium: _isPremium,
      );

      final success = draftId != null;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                success ? 'Recipe saved as draft!' : 'Failed to save draft'),
          ),
        );
        if (success) {
          widget.onSaved?.call();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfoSection(),
                    const SizedBox(height: 24),
                    _buildIngredientsSection(),
                    const SizedBox(height: 24),
                    _buildProceduresSection(),
                    const SizedBox(height: 24),
                    _buildAdvancedOptionsSection(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Basic Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Recipe Name *',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a recipe name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedDifficulty,
                    decoration: const InputDecoration(
                      labelText: 'Difficulty Level',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(
                          value: 'beginner', child: Text('Beginner')),
                      DropdownMenuItem(
                          value: 'intermediate', child: Text('Intermediate')),
                      DropdownMenuItem(
                          value: 'advanced', child: Text('Advanced')),
                    ],
                    onChanged: (value) {
                      setState(() => _selectedDifficulty = value!);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _estimatedTimeController,
                    decoration: const InputDecoration(
                      labelText: 'Time (hours) *',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Required';
                      }
                      if (int.tryParse(value) == null) {
                        return 'Invalid number';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIngredientsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Ingredients',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _ingredients
                          .add(const Ingredient(name: '', measurement: ''));
                    });
                  },
                  icon: const Icon(Icons.add),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._ingredients.asMap().entries.map((entry) {
              final index = entry.key;
              final ingredient = entry.value;
              return _buildIngredientRow(index, ingredient);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildIngredientRow(int index, Ingredient ingredient) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: TextFormField(
              initialValue: ingredient.name,
              decoration: const InputDecoration(
                labelText: 'Ingredient',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                _ingredients[index] = ingredient.copyWith(name: value);
              },
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: TextFormField(
              initialValue: ingredient.measurement,
              decoration: const InputDecoration(
                labelText: 'Amount',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                _ingredients[index] = ingredient.copyWith(measurement: value);
              },
            ),
          ),
          IconButton(
            onPressed: _ingredients.length > 1
                ? () {
                    setState(() => _ingredients.removeAt(index));
                  }
                : null,
            icon: const Icon(Icons.remove_circle_outline),
          ),
        ],
      ),
    );
  }

  Widget _buildProceduresSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Procedures',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _procedures.add(ProcedureStep(
                        stepNumber: _procedures.length + 1,
                        instruction: '',
                      ));
                    });
                  },
                  icon: const Icon(Icons.add),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._procedures.asMap().entries.map((entry) {
              final index = entry.key;
              final procedure = entry.value;
              return _buildProcedureRow(index, procedure);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildProcedureRow(int index, ProcedureStep procedure) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 16,
            child: Text('${index + 1}'),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextFormField(
              initialValue: procedure.instruction,
              decoration: const InputDecoration(
                labelText: 'Instruction',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
              onChanged: (value) {
                _procedures[index] = procedure.copyWith(
                  stepNumber: index + 1,
                  instruction: value,
                );
              },
            ),
          ),
          IconButton(
            onPressed: _procedures.length > 1
                ? () {
                    setState(() {
                      _procedures.removeAt(index);
                      // Renumber remaining procedures
                      for (int i = 0; i < _procedures.length; i++) {
                        _procedures[i] =
                            _procedures[i].copyWith(stepNumber: i + 1);
                      }
                    });
                  }
                : null,
            icon: const Icon(Icons.remove_circle_outline),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedOptionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Advanced Options',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Premium Recipe Toggle
            Consumer(
              builder: (context, ref, child) {
                final premiumStatusAsync =
                    ref.watch(premiumStatusProvider('default-device-id'));

                return premiumStatusAsync.when(
                  data: (isPremiumUser) => SwitchListTile(
                    title: const Text('Premium Recipe'),
                    subtitle: Text(isPremiumUser
                        ? 'Mark as premium content'
                        : 'Requires premium access to view'),
                    value: _isPremium,
                    onChanged: isPremiumUser
                        ? (value) {
                            setState(() => _isPremium = value);
                          }
                        : null, // Disable if not premium user
                  ),
                  loading: () => SwitchListTile(
                    title: const Text('Premium Recipe'),
                    subtitle: const Text('Loading...'),
                    value: _isPremium,
                    onChanged: null,
                  ),
                  error: (error, stack) => SwitchListTile(
                    title: const Text('Premium Recipe'),
                    subtitle: const Text('Error loading premium status'),
                    value: _isPremium,
                    onChanged: null,
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // Success Rate Slider
            Text('Success Rate: ${_successRate.toInt()}%'),
            Slider(
              value: _successRate,
              min: 0,
              max: 100,
              divisions: 20,
              label: '${_successRate.toInt()}%',
              onChanged: (value) {
                setState(() => _successRate = value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading
                ? null
                : () {
                    widget.onCancelled?.call();
                  },
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        if (widget.isDraft) ...[
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveDraft,
              child: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Save Draft'),
            ),
          ),
          const SizedBox(width: 16),
        ],
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveRecipe,
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(widget.existingRecipe != null
                    ? 'Update Recipe'
                    : 'Create Recipe'),
          ),
        ),
      ],
    );
  }
}
