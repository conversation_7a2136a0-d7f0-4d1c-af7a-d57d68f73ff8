import 'dart:convert';

import '../../domain/entities/recipe.dart';
import '../../domain/entities/ingredient.dart';
import '../../domain/entities/procedure_step.dart';
import 'ingredient_model.dart';
import 'procedure_step_model.dart';

/// Recipe model for data layer with SQLite serialization
///
/// [Source: architecture/database-schema.md]
class RecipeModel extends Recipe {
  const RecipeModel({
    required super.id,
    required super.name,
    required super.description,
    required super.ingredients,
    required super.procedures,
    required super.difficultyLevel,
    required super.estimatedTime,
    required super.isPremium,
    required super.successRate,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Create RecipeModel from Map (SQLite row)
  factory RecipeModel.fromMap(Map<String, dynamic> map) {
    // Parse ingredients from JSON
    List<Ingredient> ingredients = [];
    if (map['ingredients'] != null) {
      final ingredientsJson = jsonDecode(map['ingredients'] as String) as List;
      ingredients = ingredientsJson
          .map((json) =>
              IngredientModel.fromMap(json as Map<String, dynamic>).toEntity())
          .toList();
    }

    // Parse procedures from JSON
    List<ProcedureStep> procedures = [];
    if (map['procedures'] != null) {
      final proceduresJson = jsonDecode(map['procedures'] as String) as List;
      procedures = proceduresJson
          .map((json) =>
              ProcedureStepModel.fromMap(json as Map<String, dynamic>)
                  .toEntity())
          .toList();
    }

    return RecipeModel(
      id: map['id'] as String,
      name: map['name'] as String,
      description: map['description'] as String? ?? '',
      ingredients: ingredients,
      procedures: procedures,
      difficultyLevel: map['difficulty_level'] as String,
      estimatedTime: map['estimated_time'] as int,
      isPremium: (map['is_premium'] as int) == 1,
      successRate: (map['success_rate'] as num).toDouble(),
      createdAt: DateTime.parse(map['created_at'] as String).toLocal(),
      updatedAt: DateTime.parse(map['updated_at'] as String).toLocal(),
    );
  }

  /// Convert RecipeModel to Map for SQLite storage
  Map<String, dynamic> toMap() {
    // Serialize ingredients to JSON
    final ingredientsJson = jsonEncode(
      ingredients
          .map((ingredient) => IngredientModel.fromEntity(ingredient).toMap())
          .toList(),
    );

    // Serialize procedures to JSON
    final proceduresJson = jsonEncode(
      procedures
          .map((procedure) => ProcedureStepModel.fromEntity(procedure).toMap())
          .toList(),
    );

    return {
      'id': id,
      'name': name,
      'description': description,
      'ingredients': ingredientsJson,
      'procedures': proceduresJson,
      'difficulty_level': difficultyLevel,
      'estimated_time': estimatedTime,
      'is_premium': isPremium ? 1 : 0,
      'success_rate': successRate,
      'created_at': createdAt.toUtc().toIso8601String(),
      'updated_at': updatedAt.toUtc().toIso8601String(),
    };
  }

  /// Create RecipeModel from Recipe entity
  factory RecipeModel.fromEntity(Recipe recipe) {
    return RecipeModel(
      id: recipe.id,
      name: recipe.name,
      description: recipe.description,
      ingredients: recipe.ingredients,
      procedures: recipe.procedures,
      difficultyLevel: recipe.difficultyLevel,
      estimatedTime: recipe.estimatedTime,
      isPremium: recipe.isPremium,
      successRate: recipe.successRate,
      createdAt: recipe.createdAt,
      updatedAt: recipe.updatedAt,
    );
  }

  /// Convert to Recipe entity
  Recipe toEntity() {
    return Recipe(
      id: id,
      name: name,
      description: description,
      ingredients: ingredients,
      procedures: procedures,
      difficultyLevel: difficultyLevel,
      estimatedTime: estimatedTime,
      isPremium: isPremium,
      successRate: successRate,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  @override
  RecipeModel copyWith({
    String? id,
    String? name,
    String? description,
    List<Ingredient>? ingredients,
    List<ProcedureStep>? procedures,
    String? difficultyLevel,
    int? estimatedTime,
    bool? isPremium,
    double? successRate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RecipeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ingredients: ingredients ?? this.ingredients,
      procedures: procedures ?? this.procedures,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      isPremium: isPremium ?? this.isPremium,
      successRate: successRate ?? this.successRate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
