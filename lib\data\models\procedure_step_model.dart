import '../../domain/entities/procedure_step.dart';

/// ProcedureStep model for data layer with JSON serialization
class ProcedureStepModel extends ProcedureStep {
  const ProcedureStepModel({
    required super.stepNumber,
    required super.instruction,
    super.timing,
    super.environmentalConditions,
  });

  /// Create ProcedureStepModel from Map (JSON)
  factory ProcedureStepModel.fromMap(Map<String, dynamic> map) {
    return ProcedureStepModel(
      stepNumber: map['stepNumber'] as int,
      instruction: map['instruction'] as String,
      timing: map['timing'] as String?,
      environmentalConditions: map['environmentalConditions'] as String?,
    );
  }

  /// Convert ProcedureStepModel to Map for JSON storage
  Map<String, dynamic> toMap() {
    return {
      'stepNumber': stepNumber,
      'instruction': instruction,
      'timing': timing,
      'environmentalConditions': environmentalConditions,
    };
  }

  /// Create ProcedureStepModel from ProcedureStep entity
  factory ProcedureStepModel.fromEntity(ProcedureStep procedureStep) {
    return ProcedureStepModel(
      stepNumber: procedureStep.stepNumber,
      instruction: procedureStep.instruction,
      timing: procedureStep.timing,
      environmentalConditions: procedureStep.environmentalConditions,
    );
  }

  /// Convert to ProcedureStep entity
  ProcedureStep toEntity() {
    return ProcedureStep(
      stepNumber: stepNumber,
      instruction: instruction,
      timing: timing,
      environmentalConditions: environmentalConditions,
    );
  }

  @override
  ProcedureStepModel copyWith({
    int? stepNumber,
    String? instruction,
    String? timing,
    String? environmentalConditions,
  }) {
    return ProcedureStepModel(
      stepNumber: stepNumber ?? this.stepNumber,
      instruction: instruction ?? this.instruction,
      timing: timing ?? this.timing,
      environmentalConditions: environmentalConditions ?? this.environmentalConditions,
    );
  }
}
