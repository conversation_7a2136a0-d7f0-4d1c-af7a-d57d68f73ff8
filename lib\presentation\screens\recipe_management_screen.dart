import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/recipe.dart';
import '../../domain/entities/recipe_draft.dart';
import '../providers/core_providers.dart';
import '../widgets/usage_indicator_widget.dart';
import '../widgets/upgrade_prompt_dialog.dart';
import '../widgets/draft_management_widget.dart';
import '../widgets/advanced_search_widget.dart';
import '../../shared/services/premium_upgrade_service.dart';
import '../../shared/services/recipe_search_service.dart';
import 'recipe_form_screen.dart';
import 'recipe_detail_screen.dart';

/// Main screen for recipe management with premium-aware limits
///
/// [Source: Story 1.3 - Task 4: AC 1, 6]
class RecipeManagementScreen extends ConsumerStatefulWidget {
  const RecipeManagementScreen({super.key});

  @override
  ConsumerState<RecipeManagementScreen> createState() =>
      _RecipeManagementScreenState();
}

class _RecipeManagementScreenState extends ConsumerState<RecipeManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Recipe> _recipes = [];
  List<Recipe> _filteredRecipes = [];
  bool _isLoading = true;
  String? _error;
  Map<String, dynamic> _currentFilters = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadRecipes();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadRecipes() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final recipeRepository = ref.read(recipeRepositoryProvider);
      final recipes = await recipeRepository.getRecipes();

      setState(() {
        _recipes = recipes;
        _filteredRecipes = recipes;
        _isLoading = false;
      });

      _applyFilters({});
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _applyFilters(Map<String, dynamic> filters) async {
    setState(() {
      _isLoading = true;
      _currentFilters = filters;
    });

    try {
      final searchService = ref.read(recipeSearchServiceProvider);
      final filteredRecipes = await searchService.searchRecipes(
        query: filters['query'],
        difficultyLevel: filters['difficultyLevel'],
        isPremium: filters['isPremium'],
        ingredients: filters['ingredients']?.cast<String>(),
        maxTime: filters['maxTime'],
        minSuccessRate: filters['minSuccessRate']?.toDouble(),
      );

      setState(() {
        _filteredRecipes = filteredRecipes;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  bool _hasActiveFilters() {
    return _currentFilters.values.any((value) => value != null);
  }

  Future<void> _createNewRecipe() async {
    final deviceIdAsync = ref.read(deviceIdProvider);
    final deviceId = deviceIdAsync.when(
      data: (id) => id,
      loading: () => '',
      error: (_, __) => '',
    );

    if (deviceId.isEmpty) return;
    final recipeCountService = ref.read(recipeCountServiceProvider);
    final premiumUpgradeService = ref.read(premiumUpgradeServiceProvider);

    // Check if user can create a new recipe
    final canCreate = await recipeCountService.canCreateRecipe(deviceId);
    final shouldShowWarning =
        await recipeCountService.shouldShowWarning(deviceId);

    if (!canCreate) {
      // User has reached the limit - show upgrade prompt with draft option
      await UpgradePromptDialog.showRecipeLimitReached(
        context,
        onUpgradePressed: () => _handleUpgrade(premiumUpgradeService, deviceId),
        onSaveDraftPressed: () => _navigateToRecipeForm(isDraft: true),
      );
      return;
    }

    if (shouldShowWarning) {
      // Show warning at 8/10 recipes
      await UpgradePromptDialog.showWarningPrompt(
        context,
        onUpgradePressed: () => _handleUpgrade(premiumUpgradeService, deviceId),
      );
    }

    // Navigate to recipe form
    _navigateToRecipeForm();
  }

  Future<void> _handleUpgrade(
      PremiumUpgradeService upgradeService, String deviceId) async {
    await PremiumUpgradeService.showUpgradeBottomSheet(
        context, upgradeService, deviceId);
    // Refresh the screen after potential upgrade
    await _loadRecipes();
    setState(() {}); // Trigger rebuild to update usage indicator
  }

  void _navigateToRecipeForm({bool isDraft = false, RecipeDraft? draft}) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RecipeFormScreen(
          isDraft: isDraft,
          existingDraft: draft,
          onRecipeSaved: _loadRecipes,
        ),
      ),
    );
  }

  void _navigateToRecipeDetail(Recipe recipe) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RecipeDetailScreen(
          recipe: recipe,
          onRecipeUpdated: _loadRecipes,
          onRecipeDeleted: _loadRecipes,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Recipes'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Recipes', icon: Icon(Icons.restaurant_menu)),
            Tab(text: 'Drafts', icon: Icon(Icons.drafts)),
          ],
        ),
      ),
      body: Column(
        children: [
          // Usage Indicator
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: UsageIndicatorWidget(
              onUpgradePressed: () async {
                final deviceIdAsync = ref.read(deviceIdProvider);
                final deviceId = deviceIdAsync.when(
                  data: (id) => id,
                  loading: () => '',
                  error: (_, __) => '',
                );
                if (deviceId.isNotEmpty) {
                  final premiumUpgradeService =
                      ref.read(premiumUpgradeServiceProvider);
                  await _handleUpgrade(premiumUpgradeService, deviceId);
                }
              },
            ),
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildRecipesTab(),
                _buildDraftsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewRecipe,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildRecipesTab() {
    return Column(
      children: [
        // Advanced Search Widget
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: AdvancedSearchWidget(
            onFiltersChanged: _applyFilters,
            initialFilters: _currentFilters,
          ),
        ),

        // Recipe List
        Expanded(
          child: _buildRecipeList(),
        ),
      ],
    );
  }

  Widget _buildDraftsTab() {
    return DraftManagementWidget(
      onDraftRestore: (draft) {
        _navigateToRecipeForm(isDraft: true, draft: draft);
      },
    );
  }

  Widget _buildRecipeList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red.shade400),
            const SizedBox(height: 16),
            Text('Failed to load recipes',
                style: TextStyle(fontSize: 18, color: Colors.red.shade700)),
            const SizedBox(height: 8),
            Text(_error!,
                style: TextStyle(color: Colors.red.shade600, fontSize: 14),
                textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(onPressed: _loadRecipes, child: const Text('Retry')),
          ],
        ),
      );
    }

    if (_filteredRecipes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.restaurant_menu, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              _hasActiveFilters()
                  ? 'No recipes match your filters'
                  : 'No recipes yet',
              style: TextStyle(fontSize: 18, color: Colors.grey.shade600),
            ),
            const SizedBox(height: 8),
            Text(
              _hasActiveFilters()
                  ? 'Try adjusting your search or filters'
                  : 'Create your first recipe to get started',
              style: TextStyle(color: Colors.grey.shade500, fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadRecipes,
      child: ListView.builder(
        itemCount: _filteredRecipes.length,
        itemBuilder: (context, index) {
          final recipe = _filteredRecipes[index];
          return _buildRecipeCard(recipe);
        },
      ),
    );
  }

  Widget _buildRecipeCard(Recipe recipe) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getDifficultyColor(recipe.difficultyLevel),
          child: Text(
            recipe.difficultyLevel[0].toUpperCase(),
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(recipe.name,
            style: const TextStyle(fontWeight: FontWeight.w500)),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(recipe.description,
                maxLines: 2, overflow: TextOverflow.ellipsis),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text('${recipe.estimatedTime}h',
                    style:
                        TextStyle(color: Colors.grey.shade600, fontSize: 12)),
                const SizedBox(width: 16),
                if (recipe.isPremium) ...[
                  Icon(Icons.star, size: 16, color: Colors.amber.shade600),
                  const SizedBox(width: 4),
                  Text('Premium',
                      style: TextStyle(
                          color: Colors.amber.shade600, fontSize: 12)),
                ],
              ],
            ),
          ],
        ),
        trailing: Text('${recipe.successRate.toInt()}%',
            style: TextStyle(
                color: Colors.green.shade600, fontWeight: FontWeight.bold)),
        onTap: () => _navigateToRecipeDetail(recipe),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case 'beginner':
        return Colors.green;
      case 'intermediate':
        return Colors.orange;
      case 'advanced':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
