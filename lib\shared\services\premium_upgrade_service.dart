import 'package:flutter/material.dart';

import '../../domain/repositories/premium_repository.dart';
import '../../domain/entities/premium.dart';

/// Service for handling premium upgrade flow and purchase management
///
/// [Source: Story 1.3 - Task 2: AC 4, 7]
class PremiumUpgradeService {
  final PremiumRepository _premiumRepository;

  PremiumUpgradeService(this._premiumRepository);

  /// Initiate premium upgrade flow
  /// In a real app, this would integrate with Google Play Billing or Apple StoreKit
  Future<bool> initiateUpgrade(String userDeviceId) async {
    try {
      // TODO: Integrate with actual billing service
      // For now, simulate the upgrade process
      
      // Show upgrade options (monthly/yearly)
      // Handle payment processing
      // Verify purchase with app store
      // Update premium status
      
      // Simulated successful upgrade for development
      await _simulateUpgrade(userDeviceId);
      
      return true;
    } catch (e) {
      debugPrint('Premium upgrade failed: $e');
      return false;
    }
  }

  /// Simulate premium upgrade for development/testing
  Future<void> _simulateUpgrade(String userDeviceId) async {
    final premium = Premium(
      userDeviceId: userDeviceId,
      isPremium: true,
      purchaseToken: 'dev_token_${DateTime.now().millisecondsSinceEpoch}',
      purchaseDate: DateTime.now(),
      purchasePlatform: 'development',
      lastVerified: DateTime.now(),
    );

    await _premiumRepository.setPremiumStatus(premium);
  }

  /// Restore previous purchases
  Future<bool> restorePurchases(String userDeviceId) async {
    try {
      // TODO: Integrate with actual billing service to restore purchases
      // Query app store for previous purchases
      // Verify purchase tokens
      // Update premium status if valid purchases found
      
      debugPrint('Restore purchases not yet implemented');
      return false;
    } catch (e) {
      debugPrint('Restore purchases failed: $e');
      return false;
    }
  }

  /// Verify premium status with app store
  Future<bool> verifyPremiumStatus(String userDeviceId) async {
    try {
      final premium = await _premiumRepository.getPremiumStatus(userDeviceId);
      if (premium == null || !premium.isPremium) {
        return false;
      }

      // TODO: Verify with actual app store
      // For now, just check if we have a purchase token
      return premium.purchaseToken != null;
    } catch (e) {
      debugPrint('Premium verification failed: $e');
      return false;
    }
  }

  /// Get premium features list
  List<String> getPremiumFeatures() {
    return [
      'Unlimited recipes',
      'Advanced search and filters',
      'Recipe export and sharing',
      'Priority support',
      'Ad-free experience',
      'Cloud backup and sync',
      'Advanced analytics',
      'Custom recipe categories',
    ];
  }

  /// Get upgrade pricing options
  List<Map<String, dynamic>> getUpgradeOptions() {
    return [
      {
        'id': 'monthly',
        'title': 'Monthly Premium',
        'price': '\$4.99/month',
        'description': 'Full access to all premium features',
        'savings': null,
      },
      {
        'id': 'yearly',
        'title': 'Yearly Premium',
        'price': '\$39.99/year',
        'description': 'Full access to all premium features',
        'savings': 'Save 33%',
      },
    ];
  }

  /// Handle upgrade success
  Future<void> onUpgradeSuccess(String userDeviceId) async {
    try {
      // Refresh premium status
      await verifyPremiumStatus(userDeviceId);
      
      // TODO: Send analytics event
      // TODO: Show success message
      // TODO: Refresh UI state
      
      debugPrint('Premium upgrade successful for user: $userDeviceId');
    } catch (e) {
      debugPrint('Error handling upgrade success: $e');
    }
  }

  /// Handle upgrade failure
  void onUpgradeFailure(String error) {
    // TODO: Send analytics event
    // TODO: Show error message to user
    // TODO: Log error for debugging
    
    debugPrint('Premium upgrade failed: $error');
  }

  /// Check if user should see upgrade prompts
  Future<bool> shouldShowUpgradePrompts(String userDeviceId) async {
    try {
      final premium = await _premiumRepository.getPremiumStatus(userDeviceId);
      return premium?.isPremium != true;
    } catch (e) {
      debugPrint('Error checking upgrade prompt eligibility: $e');
      return true; // Default to showing prompts if we can't determine status
    }
  }

  /// Get upgrade call-to-action text based on context
  String getUpgradeCtaText(String context) {
    switch (context) {
      case 'recipe_limit':
        return 'Upgrade to create unlimited recipes';
      case 'search_limit':
        return 'Upgrade for advanced search features';
      case 'export_limit':
        return 'Upgrade to export and share recipes';
      default:
        return 'Upgrade to Premium';
    }
  }

  /// Show upgrade bottom sheet
  static Future<void> showUpgradeBottomSheet(
    BuildContext context,
    PremiumUpgradeService upgradeService,
    String userDeviceId,
  ) async {
    await showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _UpgradeBottomSheet(
        upgradeService: upgradeService,
        userDeviceId: userDeviceId,
      ),
    );
  }
}

/// Bottom sheet widget for premium upgrade
class _UpgradeBottomSheet extends StatelessWidget {
  final PremiumUpgradeService upgradeService;
  final String userDeviceId;

  const _UpgradeBottomSheet({
    required this.upgradeService,
    required this.userDeviceId,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),
            
            // Title
            Row(
              children: [
                Icon(Icons.star, color: Colors.amber.shade600, size: 28),
                const SizedBox(width: 8),
                const Text(
                  'Upgrade to Premium',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Features list
            ...upgradeService.getPremiumFeatures().map(
              (feature) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green.shade600, size: 20),
                    const SizedBox(width: 12),
                    Text(feature, style: const TextStyle(fontSize: 16)),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // Upgrade button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  final success = await upgradeService.initiateUpgrade(userDeviceId);
                  if (success) {
                    await upgradeService.onUpgradeSuccess(userDeviceId);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Start Free Trial',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
            ),
            const SizedBox(height: 12),
            
            // Restore purchases
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await upgradeService.restorePurchases(userDeviceId);
              },
              child: const Text('Restore Purchases'),
            ),
          ],
        ),
      ),
    );
  }
}
