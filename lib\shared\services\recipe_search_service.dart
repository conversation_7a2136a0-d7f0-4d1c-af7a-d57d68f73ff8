import '../../domain/entities/recipe.dart';
import '../../domain/repositories/recipe_repository.dart';

/// Enhanced search service for recipes with advanced filtering
///
/// [Source: Story 1.3 - Task 5: AC 6]
class RecipeSearchService {
  final RecipeRepository _recipeRepository;

  RecipeSearchService(this._recipeRepository);

  /// Search recipes with advanced filtering options
  Future<List<Recipe>> searchRecipes({
    String? query,
    String? difficultyLevel,
    bool? isPremium,
    List<String>? ingredients,
    int? maxTime,
    double? minSuccessRate,
    int? limit,
  }) async {
    try {
      // Get all recipes first
      List<Recipe> recipes = await _recipeRepository.getRecipes(
        difficultyLevel: difficultyLevel,
        isPremium: isPremium,
        limit: limit,
      );

      // Apply text search if query provided
      if (query != null && query.isNotEmpty) {
        final searchQuery = query.toLowerCase();
        recipes = recipes.where((recipe) {
          return _matchesTextSearch(recipe, searchQuery);
        }).toList();
      }

      // Apply ingredient filtering
      if (ingredients != null && ingredients.isNotEmpty) {
        recipes = recipes.where((recipe) {
          return _matchesIngredientFilter(recipe, ingredients);
        }).toList();
      }

      // Apply time filtering
      if (maxTime != null) {
        recipes = recipes.where((recipe) {
          return recipe.estimatedTime <= maxTime;
        }).toList();
      }

      // Apply success rate filtering
      if (minSuccessRate != null) {
        recipes = recipes.where((recipe) {
          return recipe.successRate >= minSuccessRate;
        }).toList();
      }

      return recipes;
    } catch (e) {
      throw Exception('Failed to search recipes: $e');
    }
  }

  /// Get search suggestions based on partial query
  Future<List<String>> getSearchSuggestions(String partialQuery) async {
    if (partialQuery.length < 2) return [];

    try {
      final recipes = await _recipeRepository.getRecipes();
      final suggestions = <String>{};
      final query = partialQuery.toLowerCase();

      for (final recipe in recipes) {
        // Add recipe name suggestions
        if (recipe.name.toLowerCase().contains(query)) {
          suggestions.add(recipe.name);
        }

        // Add ingredient suggestions
        for (final ingredient in recipe.ingredients) {
          if (ingredient.name.toLowerCase().contains(query)) {
            suggestions.add(ingredient.name);
          }
        }
      }

      return suggestions.take(10).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get popular ingredients for filter suggestions
  Future<List<String>> getPopularIngredients({int limit = 20}) async {
    try {
      final recipes = await _recipeRepository.getRecipes();
      final ingredientCounts = <String, int>{};

      for (final recipe in recipes) {
        for (final ingredient in recipe.ingredients) {
          final name = ingredient.name.toLowerCase().trim();
          if (name.isNotEmpty) {
            ingredientCounts[name] = (ingredientCounts[name] ?? 0) + 1;
          }
        }
      }

      final sortedIngredients = ingredientCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return sortedIngredients
          .take(limit)
          .map((entry) => _capitalizeFirst(entry.key))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Get filter statistics for UI
  Future<Map<String, dynamic>> getFilterStats() async {
    try {
      final recipes = await _recipeRepository.getRecipes();
      
      final difficultyStats = <String, int>{};
      final premiumCount = recipes.where((r) => r.isPremium).length;
      final avgTime = recipes.isEmpty ? 0 : 
          recipes.map((r) => r.estimatedTime).reduce((a, b) => a + b) / recipes.length;
      final avgSuccessRate = recipes.isEmpty ? 0 :
          recipes.map((r) => r.successRate).reduce((a, b) => a + b) / recipes.length;

      for (final recipe in recipes) {
        difficultyStats[recipe.difficultyLevel] = 
            (difficultyStats[recipe.difficultyLevel] ?? 0) + 1;
      }

      return {
        'totalRecipes': recipes.length,
        'premiumRecipes': premiumCount,
        'freeRecipes': recipes.length - premiumCount,
        'difficultyStats': difficultyStats,
        'averageTime': avgTime.round(),
        'averageSuccessRate': avgSuccessRate.round(),
      };
    } catch (e) {
      return {};
    }
  }

  /// Check if recipe matches text search
  bool _matchesTextSearch(Recipe recipe, String query) {
    // Search in name and description
    if (recipe.name.toLowerCase().contains(query) ||
        recipe.description.toLowerCase().contains(query)) {
      return true;
    }

    // Search in ingredients
    for (final ingredient in recipe.ingredients) {
      if (ingredient.name.toLowerCase().contains(query)) {
        return true;
      }
    }

    // Search in procedures
    for (final procedure in recipe.procedures) {
      if (procedure.instruction.toLowerCase().contains(query)) {
        return true;
      }
    }

    return false;
  }

  /// Check if recipe matches ingredient filter
  bool _matchesIngredientFilter(Recipe recipe, List<String> requiredIngredients) {
    final recipeIngredients = recipe.ingredients
        .map((i) => i.name.toLowerCase().trim())
        .where((name) => name.isNotEmpty)
        .toSet();

    final required = requiredIngredients
        .map((i) => i.toLowerCase().trim())
        .where((name) => name.isNotEmpty)
        .toSet();

    // Check if recipe contains all required ingredients
    return required.every((ingredient) {
      return recipeIngredients.any((recipeIngredient) =>
          recipeIngredient.contains(ingredient));
    });
  }

  /// Capitalize first letter of a string
  String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }

  /// Get search history (could be implemented with local storage)
  Future<List<String>> getSearchHistory() async {
    // TODO: Implement with local storage
    return [];
  }

  /// Save search query to history
  Future<void> saveSearchQuery(String query) async {
    // TODO: Implement with local storage
  }

  /// Clear search history
  Future<void> clearSearchHistory() async {
    // TODO: Implement with local storage
  }
}
