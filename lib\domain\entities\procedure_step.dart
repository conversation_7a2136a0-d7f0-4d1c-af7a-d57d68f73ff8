/// ProcedureStep entity representing individual steps in recipe procedures
///
/// [Source: architecture/data-models.md#recipe-model]
class ProcedureStep {
  final int stepNumber;
  final String instruction;
  final String? timing; // e.g., "5 minutes", "overnight"
  final String? environmentalConditions; // e.g., "sterile conditions", "room temperature"

  const ProcedureStep({
    required this.stepNumber,
    required this.instruction,
    this.timing,
    this.environmentalConditions,
  });

  ProcedureStep copyWith({
    int? stepNumber,
    String? instruction,
    String? timing,
    String? environmentalConditions,
  }) {
    return ProcedureStep(
      stepNumber: stepNumber ?? this.stepNumber,
      instruction: instruction ?? this.instruction,
      timing: timing ?? this.timing,
      environmentalConditions: environmentalConditions ?? this.environmentalConditions,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProcedureStep && 
           other.stepNumber == stepNumber && 
           other.instruction == instruction;
  }

  @override
  int get hashCode => Object.hash(stepNumber, instruction);

  @override
  String toString() {
    return 'ProcedureStep(stepNumber: $stepNumber, instruction: $instruction, timing: $timing, environmentalConditions: $environmentalConditions)';
  }
}
