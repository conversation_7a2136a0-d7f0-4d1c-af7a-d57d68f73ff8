/// Ingredient entity representing recipe ingredients with measurements
///
/// [Source: architecture/data-models.md#recipe-model]
class Ingredient {
  final String name;
  final String measurement;
  final String? notes;

  const Ingredient({
    required this.name,
    required this.measurement,
    this.notes,
  });

  Ingredient copyWith({
    String? name,
    String? measurement,
    String? notes,
  }) {
    return Ingredient(
      name: name ?? this.name,
      measurement: measurement ?? this.measurement,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Ingredient && 
           other.name == name && 
           other.measurement == measurement;
  }

  @override
  int get hashCode => Object.hash(name, measurement);

  @override
  String toString() {
    return 'Ingredient(name: $name, measurement: $measurement, notes: $notes)';
  }
}
